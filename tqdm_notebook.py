from tqdm.notebook import tqdm
import time
import numpy as np

total_iterations = 1000
data_processed = 0

for i in tqdm(range(total_iterations), desc="Processing Data", unit="iteration"):
    # 模拟数据处理
    process_time = np.random.uniform(0.01, 0.1)
    time.sleep(process_time)
    
    # 模拟处理的数据量
    data_amount = np.random.randint(10, 100)
    data_processed += data_amount
    
    # 更新进度条
    tqdm.set_postfix(data_processed=f"{data_processed/1000:.2f}k", 
                     avg_time=f"{process_time:.3f}s")