from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# WebDriverChromeChromeDriver
driver = webdriver.Chrome()


driver.get("http://tech.ccb.com/ssologin?appId=OTk0MjAyMTAzMjcyMDAwMzM5MTk3Mzk0&target=687474703a2f2f61692e6a682f72657365617263682f646174617365742f6163636573732f6f76657276696577") 


username = driver.find_element(By.XPATH, "//input[@placeholder='请输入UASS用户名']")
password = driver.find_element(By.XPATH, "//input[@placeholder='请输入UASS密码']")

login_button = driver.find_element(By.CLASS_NAME, "loginBtn1 vcodeBtn ivu-btn ivu-btn-primary ivu-btn-long")

username.send_keys("houwei.jx")  # 
password.send_keys("Jx8684821")  # 
login_button.click()


WebDriverWait(driver, 10).until(
    EC.presence_of_element_located((By.XPATH, "//*[@id='app']/aside/div/div/div/div/input[placeholder='0001江西分行实验项目']"))
)

driver.find_element(By.XPATH, '//*[@id="app"]/aside/ul/li[1]/div/span').click()

driver.find_element(By.XPATH, '//*[@id="app"]/aside/ul/li[1]/ul/div/li[1]').click()

driver.find_element(By.XPATH, '//*[@id="app"]/main/div/div[2]/div/div[2]/div[2]/div[5]/div[2]/table/tbody/tr[1]/td[8]/div/button').click()


 
driver.quit()