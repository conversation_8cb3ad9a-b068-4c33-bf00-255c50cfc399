import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def feature_engineering(data_path, output_path=None):
    """
    对明细数据进行特征工程，计算按ID的各种统计量
    
    Parameters:
    data_path (str): 输入数据的路径，CSV格式，包含rq(日期),id,lx(类型),tm(持续时间),cs(次数)
    output_path (str, optional): 输出结果的路径，如果为None则不保存结果
    
    Returns:
    pd.DataFrame: 包含特征工程结果的数据框
    """
    # 读取数据
    df = pd.read_csv(data_path)
    
    # 确保日期列是日期时间格式
    df['rq'] = pd.to_datetime(df['rq'])
    
    # 获取当前日期（使用数据中最新的日期，假设数据是最新的）
    latest_date = df['rq'].max()
    
    # 计算上个月的起止日期
    last_month_end = latest_date.replace(day=1) - timedelta(days=1)
    last_month_start = last_month_end.replace(day=1)
    
    # 计算上上个月的起止日期
    last_2_month_end = last_month_start - timedelta(days=1)
    last_2_month_start = last_2_month_end.replace(day=1)
    
    # 计算前三个月的起止日期（上个月+上上个月+上上上个月）
    last_3_month_end = last_month_end
    last_3_month_start = (last_2_month_start - timedelta(days=1)).replace(day=1)
    
    # 计算前6个月的起止日期
    last_6_month_end = last_month_end
    temp_date = last_month_start
    for _ in range(5):  # 往前推5个月（加上当前月就是6个月）
        temp_date = (temp_date - timedelta(days=1)).replace(day=1)
    last_6_month_start = temp_date
    
    # 筛选上个月的数据
    last_month_data = df[(df['rq'] >= last_month_start) & (df['rq'] <= last_month_end)]
    
    # 筛选前三个月的数据
    last_3_month_data = df[(df['rq'] >= last_3_month_start) & (df['rq'] <= last_3_month_end)]
    
    # 筛选前六个月的数据
    last_6_month_data = df[(df['rq'] >= last_6_month_start) & (df['rq'] <= last_6_month_end)]
    
    # 按ID进行分组统计
    result = pd.DataFrame()
    result['id'] = df['id'].unique()
    
    # 计算各时间段的特征
    # 每次调用calculate_features都会返回添加了新特征的数据框
    # 我们将结果赋值回result变量，以便在下一次调用时包含之前添加的特征
    result = calculate_features(result, last_month_data, "上个月")  # 添加上个月的特征
    result = calculate_features(result, last_3_month_data, "前三个月")  # 添加前三个月的特征
    result = calculate_features(result, last_6_month_data, "前六个月")  # 添加前六个月的特征
    
    # 填充NaN值为0
    result.fillna(0, inplace=True)
    
    # 保存结果（如果需要）
    if output_path:
        result.to_csv(output_path, index=False, encoding='utf-8-sig')
    
    return result

def calculate_features(result_df, data, time_period_name):
    """
    计算指定时间段的统计特征
    
    Parameters:
    result_df (pd.DataFrame): 结果数据框
    data (pd.DataFrame): 指定时间段的数据
    time_period_name (str): 时间段名称
    
    Returns:
    pd.DataFrame: 添加了新特征的数据框
    """
    # 所有类型的统计
    # 累计次数
    counts = data.groupby('id')['cs'].sum().reset_index()
    result_temp = pd.merge(result_df, counts, on='id', how='left')
    result_temp.rename(columns={'cs': f'{time_period_name}累计次数'}, inplace=True)
    
    # 累计时间
    times = data.groupby('id')['tm'].sum().reset_index()
    result_temp = pd.merge(result_temp, times, on='id', how='left')
    result_temp.rename(columns={'tm': f'{time_period_name}累计时间'}, inplace=True)
    
    # 类型=3的统计
    type_3_data = data[data['lx'] == 3]
    
    # 类型=3的累计次数
    type_3_counts = type_3_data.groupby('id')['cs'].sum().reset_index()
    result_temp = pd.merge(result_temp, type_3_counts, on='id', how='left')
    result_temp.rename(columns={'cs': f'类型=3的{time_period_name}累计次数'}, inplace=True)
    
    # 类型=3的累计时间
    type_3_times = type_3_data.groupby('id')['tm'].sum().reset_index()
    result_temp = pd.merge(result_temp, type_3_times, on='id', how='left')
    result_temp.rename(columns={'tm': f'类型=3的{time_period_name}累计时间'}, inplace=True)
    
    return result_temp

# 示例使用
if __name__ == "__main__":
    # 替换为实际的数据文件路径
    input_path = "data.csv"
    output_path = "feature_engineering_result.csv"
    
    result_df = feature_engineering(input_path, output_path)
    print("特征工程完成！结果已保存至:", output_path)
    print(result_df.head()) 