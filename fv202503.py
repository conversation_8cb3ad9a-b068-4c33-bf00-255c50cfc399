#!/usr/bin/env python
# coding: utf-8

# In[99]:


#coding=utf-8

import pandas as pd
import numpy as np
import sys
from sklearn.model_selection import train_test_split
#import xgboost as xgb
import lightgbm as lgb
#import catboost as ctb
from sklearn.ensemble import RandomForestClassifier
from sklearn import metrics
from sklearn.ensemble import StackingClassifier
from sklearn.model_selection import StratifiedShuffleSplit
from sklearn.model_selection import KFold, StratifiedKFold,GroupKFold
from sklearn.metrics import f1_score
from sklearn import preprocessing
import joblib
import sys
import calendar
from tqdm import tqdm
from datetime import datetime
from dateutil.relativedelta import relativedelta
from pandas.tseries.offsets import Day, MonthEnd
from get_date import get_date
from is_jupyter import is_jupyter

pd.set_option("display.max_columns", 1500)
pd.set_option("display.max_rows", 1500)


# In[100]:


class BetaEncoder(object):
        
    def __init__(self, group):
        
        self.group = group
        self.stats = None
        
    # get counts from df
    def fit(self, df, target_col):
        # 先验均值
        self.prior_mean = np.mean(df[target_col]) 

        stats = df[[target_col, self.group]].groupby(self.group)
        # count和sum
        stats           = stats.agg(['sum', 'count'])[target_col]    
        stats.rename(columns={'sum': 'n', 'count': 'N'}, inplace=True)
        stats.reset_index(level=0, inplace=True)           
        self.stats      = stats
        
    # extract posterior statistics
    def transform(self, df, stat_type, N_min=1):
        
        df_stats = pd.merge(df[[self.group]], self.stats, how='left')
        n        = df_stats['n'].copy()
        N        = df_stats['N'].copy()
        
        # fill in missing
        nan_indexs    = np.isnan(n)
        n[nan_indexs] = self.prior_mean
        N[nan_indexs] = 1.0
        
        # prior parameters
        N_prior     = np.maximum(N_min-N, 0)
        alpha_prior = self.prior_mean * N_prior
        beta_prior  = (1-self.prior_mean) * N_prior
        
        # posterior parameters
        alpha       =  alpha_prior + n
        beta        =  beta_prior  + N-n
        
        # calculate statistics
        if stat_type=='mean':
            num = alpha
            dem = alpha + beta
                    
        elif stat_type=='mode':
            num = alpha - 1
            dem = alpha + beta - 2
            
        elif stat_type=='median':
            num = alpha - 1 / 3
            dem = alpha + beta - 2 / 3
        
        elif stat_type=='var':
            num = alpha * beta
            dem = (alpha + beta)**2 * (alpha + beta + 1)
                    
        elif stat_type=='skewness':
            num = 2*(beta-alpha)*np.sqrt(alpha+beta+1)
            dem = (alpha+beta+2)*np.sqrt(alpha*beta)

        elif stat_type=='kurtosis':
            num = 6*(alpha-beta)**2*(alpha+beta+1) - alpha*beta*(alpha+beta+2)
            dem = alpha*beta*(alpha+beta+2)*(alpha+beta+3)
            
        # replace missing
        value = num / dem
        value[np.isnan(value)] = np.nanmedian(value)
        return value


# In[101]:


# 获取客户外呼次数、进件次数，按客户外呼日期，取全量数据。
# 测试数据设置外呼日期匹配
# category：
#     3： 外呼
#     4： 进件
    
    
def cre_calling_instm(calling_instm_file):
    
    cols_name = ['cst_keywrd', 'category', 'apply_dt', 'date2', 'time1', 'instm_pd_tpcd', 'apply_num']
    
    df=pd.read_csv('/home/<USER>/work/fqt/csv/' + calling_instm_file, header=None, sep=',', usecols=[0, 1, 2, 3, 4, 5, 6], names=cols_name, dtype={'cst_keywrd' : 'object'}, parse_dates=['apply_dt'])
    
    
    df_call = df[(df.category==3) & (df.cst_keywrd.notnull())].copy()
    df_instm = df[(df.category==4) & (df.cst_keywrd.notnull())].copy()
    
    df_instm_03 = df_instm[df_instm.instm_pd_tpcd==3].copy()    

    df_call_g = df_call.set_index(['apply_dt'])
    df_call_g.sort_index(inplace=True)
    df_call_g = df_call_g.groupby('cst_keywrd')['instm_pd_tpcd', 'apply_num'].rolling('540D').sum().reset_index()
    df_call_g.columns = ['cst_keywrd', 'call_dt', 'call_conn_num', 'call_num']
    
    df_instm_03_g = df_instm_03.set_index(['apply_dt'])
    df_instm_03_g.sort_index(inplace=True)
    df_instm_03_g = df_instm_03_g.groupby('cst_keywrd')['apply_num'].rolling('540D').sum().reset_index()
    df_instm_03_g.columns = ['cst_keywrd', 'call_dt', 'instm_03_num']

    df_instm_g = df_instm.copy().set_index(['apply_dt'])
    df_instm_g.sort_index(inplace=True)
    df_instm_g = df_instm_g.groupby('cst_keywrd')['apply_num'].rolling('540D').sum().reset_index()
    df_instm_g = df_instm_g.groupby(['cst_keywrd', 'apply_dt']).max('apply_num').reset_index()
    df_instm_g.columns = ['cst_keywrd', 'call_dt', 'instm_num']

    df_instm_cat_g = df_instm.copy().set_index(['apply_dt'])
    df_instm_cat_g.sort_index(inplace=True)
    df_instm_cat_g = df_instm_cat_g.groupby('cst_keywrd')['instm_pd_tpcd'].rolling('540D').apply(pd.Series.nunique, raw=False)
    df_instm_cat_g = df_instm_cat_g.groupby(['cst_keywrd', 'apply_dt']).max('instm_pd_tpcd').reset_index()
    df_instm_cat_g.columns = ['cst_keywrd', 'call_dt', 'instm_cat_num']    
    
    return df_call_g, df_instm_03_g, df_instm_g, df_instm_cat_g


# 

# In[102]:


############# 参数 ###############
# in_file:   原始文件，来自于mpp
# u_cache：  历史进件使用外呼特征缓存：True
##################################

def cr_feature(in_file):
    
    cols_name = ['cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind','age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind', 'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind', 'stm_evl_cst_grd_cd', 'mo_incmam', 
        'cstmgr_id_ind', 'best_ctc_tel_ind', 'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind', 'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind', 'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd', 'eddgr_cd', 
        'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd', 'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind','tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd',
            'pyrl_cst_ind', 'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind', 'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind', 'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 
            'ms_dep_sign_ind', 'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind', 'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind', 'brkevn_chrtc_cst_ind', 
            'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind', 'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind', 'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind', 
            'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind', 'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind', 'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind', 
            'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal', 'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd', 'CrnMo_Entmnt_Cgy_Cnsmp_Amt', 'CrnMo_Entmnt_Cgy_CsmDnum', 
            'CrnMo_RlEst_Cgy_Cnsmp_Amt', 'CrnMo_RlEst_Cgy_CsmDnum', 'CrnMo_Car_SCgy_Cnsmp_Amt', 'CrnMo_Car_SCgy_CsmDnum', 'CrnMo_Whlsl_Cgy_Cnsmp_Amt', 'CrnMo_Whlsl_Cgy_CsmDnum', 'CrnMo_LgHmAppls_Cgy_Cnsmp_Amt', 
            'CrnMo_LgHmAppls_Cgy_CsmDnum', 'CrnMo_PbHosp_Cgy_Cnsmp_Amt', 'CrnMo_PbHosp_Cgy_CsmDnum', 'CrnMo_LgTp_Entp_Pch_Cgy_Cnsmp_Amt', 'CrnMo_LgTp_Entp_Pch_Cgy_CsmDnum', 'CrnMo_HcBty_Cgy_Cnsmp_Amt', 
            'CrnMo_HcBty_Cgy_CsmDnum', 'CrnMo_Tvl_Cgy_Cnsmp_Amt', 'CrnMo_Tvl_Cgy_CsmDnum', 'CrnMo_TaxPymt_Cgy_Cnsmp_Amt', 'CrnMo_TaxPymt_Cgy_CsmDnum', 'CrnMo_Car_Svc_Cgy_Cnsmp_Amt', 'CrnMo_Car_Svc_Cgy_CsmDnum',
            'RT3Mo_Entmnt_Cgy_Cnsmp_Amt', 'RT3Mo_Entmnt_Cgy_CsmDnum', 'RT3Mo_RlEst_Cgy_Cnsmp_Amt', 'RT3Mo_RlEst_Cgy_CsmDnum', 'RT3Mo_Car_SCgy_Cnsmp_Amt', 'RT3Mo_Car_SCgy_CsmDnum', 'RT3Mo_Whlsl_Cgy_Cnsmp_Amt', 
            'RT3Mo_Whlsl_Cgy_CsmDnum', 'RT3Mo_LgHmAppls_Cgy_Cnsmp_Amt', 'RT3Mo_LgHmAppls_Cgy_CsmDnum', 'RT3Mo_PbHosp_Cgy_Cnsmp_Amt', 'RT3Mo_PbHosp_Cgy_CsmDnum', 'RT3Mo_LgTp_Entp_Pch_Cgy_Cnsmp_Amt', 
            'RT3Mo_LgTp_Entp_Pch_Cgy_CsmDnum', 'RT3Mo_HcBty_Cgy_Cnsmp_Amt', 'RT3Mo_HcBty_Cgy_CsmDnum', 'RT3Mo_Tvl_Cgy_Cnsmp_Amt', 'RT3Mo_Tvl_Cgy_CsmDnum', 'RT3Mo_TaxPymt_Cgy_Cnsmp_Amt', 'RT3Mo_TaxPymt_Cgy_CsmDnum', 
            'RT3Mo_Car_Svc_Cgy_Cnsmp_Amt', 'RT3Mo_Car_Svc_Cgy_CsmDnum', 'mpbcstcmumblphmodlnum', 'mpbcstr12mumphmodlnum', 'mpbcstcrnmousmblphnum', 'mpbcstrt12musmblphnum', 'mpbcstroumpmodllstdyr', 
            'mpbcstroumpmllstdhprc', 'mpbcstroumpmllstdlprc', 'holdhhedmblph_cst_ind', 'holdnwtpmblph_cst_ind', 'freqrplcmblph_cst_ind', 'ntw_bnk_sign_ind', 'mfs_sign_ind', 'dep_cst_ind', 'dmddep_cst_ind', 
            'tmdep_cst_ind', 'cny_dep_ind', 'frncy_dep_cst_ind', 'xbrdrfnc_cst_ind', 'loan_cst_ind', 'fsln_cst_ind', 'inclsvln_cst_ind', 'chmtpd_cst_ind', 'ccb_chmtpd_cst_ind', 'bond_cst_ind', 'vchr_natdbt_cst_ind',
                'fnd_cst_ind', 'stk_fnd_cst_ind', 'bld_fnd_cst_ind', 'bond_fnd_cst_ind', 'ccy_mkt_fnd_cst_ind', 'qdii_fnd_cst_ind', 'clsd_fnd_cst_ind', 'othr_fnd_cst_ind', 'ins_cst_ind', 'scr_fnds_mgt_cst_ind', 
                'ccrdcst_ind', 'ccrd_vld_cst_ind', 'ccrd_avy_cst_ind', 'ccrd_actvt_cst_ind', 'preex_btch_cst_ind', 'crcrd_rcsixmo_txn_ind', 'crcrd_instm_cst_ind', 'gldcrd_cst_ind', 'pcrd_cst_ind', 
                'wlth_mgt_crd_cst_ind', 'prvt_bnk_crd_cst_ind', 'obnk_prvt_bnk_crd_cst_ind', 'socins_lcrd_cst_ind', 'setlexp_crd_cst_ind', 'fushun_lcrd_cst_ind', 'prvdfnd_lcrd_cst_ind', 'dapeng_iccrd_cst_ind', 
                'car_crd_cst_ind', 'tra_lcrd_cst_ind', 'glb_py_crd_cst_ind', 'mall_mbsh_ind', 'rt12mo_srbsn_ind', 'rt12mocvnlvng_ind', 'stdntprft_svc_sgn_ind', 'rt12mo_mopcml_txcst_ind', 'rt12mo_acpsmtl_txcst_ind', 
                'rt12mo_agncgdepm_txcst_ind', 'rt12mo_lpay_cst_ind', 'accgld_cst_ind', 'gnstg_mopcml_cst_ind', 'acpsmtl_sign_ind', 'acc_fx_sign_ind', 'gld_ostck_sign_ind', 'eysv_gld_sign_ind', 'epos_sign_ind', 
                'fstfl_cst_ind', 'idvfamcshmgtsvcsgnind_ind', 'accgld_bidirect_sign_ind', 'gldbutler_cst_ind', 'micro_gld_cst_ind', 'rt12mo_pyrl_cst_ind', 'rt12mo_fncpyrl_cst_ind', 'epa_cst_ind', 'etc_cst_ind', 
                'etc_sign_ind', 'ebnk_sign_st_ind', 'mpb_sign_st_ind', 'dbcrd_bndg_wechat_ind', 'crcrd_bndg_wechat_ind', 'hgst_setl_acc_clcd_ind', 'estb_1_cgy_acc_cst_ind', 'estb_2_cgy_acc_cst_ind', 
                'estb_3_cgy_acc_cst_ind', 'cust_type_ind', 'corp_name_ind', 'stmt_adr_zipecd_ind', 'thismon_new_ind', 'lastmon_new_ind', 
                'yx_level_ind', 'sp_level_ind', 'cst_label_ind', 'yx_score']
    
    cols_num = np.arange(227)
    df_cust = pd.read_csv('/home/<USER>/work/fqt/csv/' + in_file,  header=None, sep=',', usecols=cols_num, names=cols_name, dtype={'cst_keywrd':object}, converters={'cst_blng_insid':str}, parse_dates=['call_dt'])

    col_drops = ['date2', 'current_time']
    df_cust.drop(col_drops, inplace=True, axis=1)
    
    df_cust['mpbcstroumpmodllstdyr'].fillna(9999, inplace=True)
    df_cust['mpbcstroumpmodllstdyr'] = df_cust['mpbcstroumpmodllstdyr'].apply(lambda x :  pd.to_numeric(x, errors='coerce'))
    
    
    df_cust['ocp_cd'].fillna(9999, inplace=True)
    df_cust['ocp_cd'] = df_cust['ocp_cd'].apply(lambda x :  pd.to_numeric(x, errors='coerce'))
             
  
        
    
    drop_cols = [
        'rt12mo_srbsn_ind',
        'rt12mo_agncgdepm_txcst_ind',
        'Cash_BACK_Cnt_L3',
        'eysv_gld_sign_ind',
        'ruralres_ind',
        'soldier_ind',
        'vchr_natdbt_cst_ind',
        'wlth_mgt_crd_cst_ind',
        'prvt_bnk_crd_cst_ind',
        'Cash_BACK_Amt_L2',
        'fushun_lcrd_cst_ind',
        'idv_oprt_lncst_ind',
        'idv_cnsmp_lncst_ind',
        'natdbt_cst_ind',
        'nine_elmt_compl_cst_ind',
        'glb_py_crd_cst_ind',
        'hiqlty_idy_cst_ind',
        'non_rsdnt_cst_ind',
        'Cash_BACK_Amt_L8',
        'gnstg_mopcml_cst_ind',
        'othr_fnd_cst_ind',
        'ovsea_cst_ind',
        'Cash_BACK_Amt_L11',
        'Sum_Draw_Cnt_L5',
        'STMT_CASH_INTR_L5',
        'Instm_Grd_Amt_L6',
        'Instm_Grd_Amt_L5',
        'Instm_Grd_Amt_L3',
        'Rt6_Prd_CsmDnum',
        'Instm_Grd_Amt_L2',
        'Instm_Grd_Amt_L11',
        'Instm_Fst_Dnum_L9',
        'STMT_CASH_INTR_L10',
        'STMT_CASH_INTR_L3',
        'STMT_CASH_INTR_L7',
        'Sum_Consume_Amt_L6',
        'Instm_Fst_Dnum_L3',
        'STMT_RTL_INTR_L3',
        'Instm_Fst_Dnum_L11',
        'Instm_Fst_Amt_L7',
        'Sum_Consume_Amt',
        'Sum_Consume_Amt_L10',
        'Instm_Fst_Amt_L5',
        'Sum_Consume_Amt_L2',
        'Sum_Consume_Amt_L3',
        'Instm_Grd_Amt_L8',
        'Rt12_Prd_Encshmt_Int_MoNum',
        'Instm_Grd_Dnum_L3',
        'Repy_Dnum_L8',

        'RT3Mo_Cnsmp_Instm_Dnum',
        'RT3Mo_InstmT_Amt',
        'RT3Mo_InstmT_Dnum',

        'Repy_Amt_L11',
        'Repy_Amt_L3',
        'Instm_Grd_Dnum_L8',
        'Repy_Amt_L7',
        'Repy_Amt_L9',
        'Instm_Grd_Dnum_L6',
        'Repy_Dnum_L11',
        'Repy_Dnum_L3',
        'Repy_Dnum_L5',
        'Repy_Dnum_L6',
        'Repy_Dnum_L7',
        'Sum_Consume_Amt_L4',
        'Sum_Consume_Amt_L8',
        'Cash_Tfrout_Cnt_L2',
        'CrCrd_HCpln_Trnd_Cst_Ind',
        'bigamt_realest_own_cst_ind',
        'bond_cst_ind',
        'brkevn_chrtc_cst_ind',
        'Crcrd_Cst_Rt3Mo_ActDeal_Cnt',
        'Crcrd_Cst_Rt2Mo_ActDeal_Cnt',
        'ccb_fam_mbr_ind',
        'Crcrd_Cst_Rt1Yr_ActDeal_Cnt',
        'Crcrd_Cst_Rt1Mo_ActDeal_Cnt',
        'CrCrd_HCpln_Trnd_Dgr_Cd',
        'clsd_fnd_cst_ind',
        'Sum_Consume_Amt_L9',
        'Cnsmp_Instm_Bill_Bal',
        'Cash_Tfrout_Amt_L7',
        'Cash_Tfrout_Amt_L3',
        'Cash_Tfrout_Amt_L2',
        'dapeng_iccrd_cst_ind',
        'dep_cst_ind',
        'Cash_Tfrout_Cnt_L9',
        'Cash_Tfrout_Cnt_L3',
        'CrnMo_Cnsmp_Instm_Amt',
        'CrnMo_Com_Instm_Dnum',
        'CrnMo_InstmT_Amt',
        'CrnMo_InstmT_Dnum',
        'Sum_Consume_Cnt_L1',
        'Sum_Consume_Cnt_L10',
        'Sum_Consume_Cnt_L3',
        'Sum_Consume_Cnt_L5',
        'Instm_Fst_Amt_L3',
        'Instm_Fst_Amt_L10',
        'Sum_Draw_Amt_L2',
        'Sum_Draw_Amt_L3',
        'Sum_Draw_Amt_L8',
        'Sum_Draw_Amt_L9',
        'Sum_Draw_Cnt_L10',
        'Sum_Draw_Cnt_L11',
        'Sum_Draw_Cnt_L3',
        'CrnPrd_Cnsmp_Amt',
        'Sum_Draw_Cnt_L6',
        'Sum_Draw_Cnt_L7',
        'acc_fx_sign_ind',
        'CrnMo_SpInstm_Amt',
        'antmylndg_clbr_cst_ind',
        'xbrdrfnc_cst_ind',
#        2023-6-29增加
        'Cash_BACK_Amt_L9',
        'Sum_Draw_Amt_L10',
        'Instm_Grd_Dnum_L7',
        'Sum_Consume_Cnt_L8',
        'SpInstm_Cur_Bal',
        'STMT_RTL_INTR_L1',
        'Rt6_Prd_Encshmt_Amt',
        'RT3Mo_SpInstm_Amt',
        'RT3Mo_Cnsmp_Instm_Amt',
        'RT12Mo_InstmT_Dnum',
        'Sum_Draw_Amt_L1',
        'Instm_Grd_Amt_L1',
        'Instm_Fst_Amt_L4',
        'Instm_Cur_Bal',
        'CrnMo_SpInstm_Dnum',
        'CrnMo_Cnsmp_Instm_Dnum',
        'Com_Instm_Cur_Bal',
        'Cash_Tfrout_Amt_L4',
        'Instm_Grd_Dnum_L5',
        'ruralres_ind',
#        4.4新增
        'rt12mo_srbsn_ind',
        'Instm_Fst_Dnum_L6',
        'rt12mo_mopcml_txcst_ind',
        'soldier_ind',
        'Cash_BACK_Cnt_L9',
        'Cash_BACK_Amt_L3',
        'vchr_natdbt_cst_ind',
        'wlth_mgt_crd_cst_ind',
        'rt12mo_agncgdepm_txcst_ind',
        'hiqlty_idy_cst_ind',
        'qdii_fnd_cst_ind',
        'antmylndg_clbr_cst_ind',
        'glb_py_crd_cst_ind',
        'eysv_gld_sign_ind',
        'dapeng_iccrd_cst_ind',
        'clsd_fnd_cst_ind',
        'ccb_fam_mbr_ind',
        'brkevn_chrtc_cst_ind',
        'bond_cst_ind',
        'bigamt_realest_own_cst_ind',
        'best_pn_ind',
        'acc_fx_sign_ind',
        'prvt_bnk_crd_cst_ind',
        'Sum_Consume_Amt_L5',
        'natdbt_cst_ind',
        'nine_elmt_compl_cst_ind',
        'non_rsdnt_cst_ind',
        'Rt3_Prd_Cnsmp_Amt',
        'othr_fnd_cst_ind',
        'Repy_Dnum_L10',
        'gnstg_mopcml_cst_ind',
        'Repy_Amt',
        'xbrdrfnc_cst_ind',
        'stmt_adr_ziped',
# 8.0新增
        'cny_dep_ind',
        'Sum_Draw_Cnt_L2',
        'CrnMo_LgTp_Entp_Pch_Cgy_Cnsmp_Amt',
        'CrnMo_Tvl_Cgy_CsmDnum',
        'fushun_lcrd_cst_ind',
        'RT6Mo_Cnsmp_Instm_Amt',
        'Cash_Tfrout_Cnt_L5',
        'Instm_Fst_Dnum',
        'Rt3_Prd_DealW_Bill_Instm_Dnum',
        'Cash_Tfrout_Cnt',
        'CrnMo_Bill_Instm_Dnum',
        'RT3Mo_Com_Instm_Dnum',
        'CrnMo_Instm_Dnum',
        'CrnMo_PbHosp_Cgy_CsmDnum',
        'Cash_Tfrout_Cnt_L4',
        'Cash_BACK_Cnt_L6',
        'RT3Mo_Bill_Instm_Dnum',
        'CrnMo_Car_SCgy_CsmDnum',
        'RT3Mo_LgTp_Entp_Pch_Cgy_Cnsmp_Amt',
        'RT6Mo_InstmT_Amt',
        'CrnMo_Cash_Instm_Amt',
        'RT6Mo_Cnsmp_Instm_Dnum',
        'idv_ovsea_rmt_cst_ind',
        'RT6Mo_InstmT_Dnum',
        'crcrd_crline',
        'PrvtCstCT910DPALDDnum',
        'tyni_ccb_cst_ind',
        'tmdep_cst_ind',
        'dmddep_cst_ind',
        'acc_cmdty_cst_ind',
        'carln_cst_ind',
        'RT3Mo_RlEst_Cgy_Cnsmp_Amt',
        'CrnMo_LgTp_Entp_Pch_Cgy_CsmDnum',
        'lcs_cd',
        'instm_count_ind',
        'CrnMo_RlEst_Cgy_Cnsmp_Amt',
        'CrnMo_Cash_Instm_Dnum',
        'CrnMo_RlEst_Cgy_CsmDnum',
        'ms_dep_sign_ind',
        'non_brkevn_chrtc_cst_ind',
        'idvfamcshmgtsvcsgnind_ind',
        'agnc_gldexg_cst_ind',
        'opn_chmtpd_cst_ind',
        'idv_oprt_lncst_ind',
        'idv_cnsmp_lncst_ind',
        'RT3Mo_RlEst_Cgy_CsmDnum',
        'bigamt_ctfofdep_cst_ind',
        'qckln_cst_ind',
        'accgld_atim_cst_ind',
        'Cash_BACK_Cnt_L4',
        'Cash_BACK_Cnt',
        
        'STMT_RTL_INTR',
        'STMT_RTL_INTR_L4',
        'Rt3_Prd_Encshmt_Dnum',
        'RT3Mo_SpInstm_Dnum',
        'RT6Mo_Cash_Instm_Dnum',
        'PrvtCstCT910DZCFDDnum',
        'Cash_BACK_Amt',
        'CrnMo_TaxPymt_Cgy_CsmDnum',
        'estb_1_cgy_acc_cst_ind',
        'Rt6_Prd_DealW_Bill_Instm_Dnum',
        'Cash_BACK_Amt_L4',
        'CrnMo_HcBty_Cgy_CsmDnum',

        'RT3Mo_LgTp_Entp_Pch_Cgy_CsmDnum',
        'Instm_Fst_Dnum_L2',
        'Sum_Draw_Cnt_L4',
        'Cash_Tfrout_Amt',
        'RT3Mo_Car_SCgy_CsmDnum',
        'RT3Mo_Cash_Instm_Dnum',
        'CrnPrd_Encshmt_Dnum',
        'apply_num_ind',
        'Cash_Tfrout_Cnt_L6_ind',
        'Cash_BACK_Cnt_L5',
        'idv_frncy_cst_ind',
        'Cash_BACK_Amt_L5',
        'Sum_Draw_Cnt',
        'Sum_Draw_Amt',
        'mpb_sign_not_actvt_ind',
        'instm_count_ind'
        
        'RT3Mo_TaxPymt_Cgy_CsmDnum',
        'Sum_Draw_Amt_L4',
        'Cash_BACK_Amt_L1',
        'pcrd_cst_ind',
        'stk_fnd_cst_ind',
        'ccrd_vld_cst_ind',
        'ccrdcst_ind',
        'RT3Mo_PbHosp_Cgy_Cnsmp_Amt',
        'epos_sign_ind',
        'Cash_Tfrout_Amt_L5',
        'CrnMo_Com_Instm_Amt',
        'Instm_Fst_Amt_L2',
        'holdhhedmblph_cst_ind',
        'CrnMo_Tvl_Cgy_Cnsmp_Amt',
        'CrnMo_Car_SCgy_Cnsmp_Amt',
        'CrnMo_Bill_Instm_Amt',
        'Sum_Draw_Amt_L6',
        'idvexstl_cst_ind',
        'gld_ostck_sign_ind',
        'RT3Mo_Tvl_Cgy_CsmDnum',
        'fstfl_cst_ind',
        'Instm_Fst_Dnum_L5',
        'Instm_Fst_Dnum_L4',
        'C_Cash_Use_Rate',
        'rt12mo_acpsmtl_txcst_ind',
        'Sum_Draw_Cnt_L1',
        'RT6Mo_SpInstm_Dnum',
        'holdnwtpmblph_cst_ind',
        'stdntprft_svc_sgn_ind',
        'impt_psng_ind',
        'RT3Mo_Cash_Instm_Amt',
        'RT6Mo_Com_Instm_Dnum',
        'CrnPrd_Encshmt_Amt',
        'RT3Mo_PbHosp_Cgy_CsmDnum',
        'RT6Mo_Bill_Instm_Dnum',
        'STMT_CASH_INTR',
        'RT3Mo_Instm_Dnum',
        'wechat_bnk_sign_ind',
        'CrnMo_PbHosp_Cgy_Cnsmp_Amt',
        'CrnMo_LgHmAppls_Cgy_CsmDnum',
        'instm_nunique_ind',
        'Cash_Tfrout_Amt_L6',
        'Instm_Fst_Dnum_L1',
        'CrnMo_Whlsl_Cgy_CsmDnum',
        'instm_count_ind',
        'apply_num_ind'

    ]
    
    df_cust.drop(drop_cols, axis=1, inplace=True, errors='ignore')
    
    df_call_g = joblib.load('/home/<USER>/work/fqt/cache/df_call_g.pkl')
    df_instm_g = joblib.load('/home/<USER>/work/fqt/cache/df_instm_g.pkl')
    df_instm_03_g = joblib.load('/home/<USER>/work/fqt/cache/df_instm_03_g.pkl')
    df_instm_cat_g = joblib.load('/home/<USER>/work/fqt/cache/df_instm_cat_g.pkl')
    
    
    
    df_call = df_cust.drop_duplicates(subset=['cst_keywrd', 'call_dt']).loc[:, ['cst_keywrd', 'call_dt']]
    
    df_cust_call = df_call.copy()
    df_cust_call = df_cust_call.merge(df_call_g, on=['cst_keywrd'], how='left')
    df_cust_call = df_cust_call[((df_cust_call.call_dt_x-df_cust_call.call_dt_y)>=pd.Timedelta(0,'D')) & ((df_cust_call.call_dt_x-df_cust_call.call_dt_y)<pd.Timedelta(540,'D')) ]
    df_cust_call = df_cust_call.groupby(['cst_keywrd', 'call_dt_x']).max('call_dt_y').reset_index() 
    df_cust_call.rename(columns={'call_dt_x' : 'call_dt'}, inplace=True)

    df_cust_instm = df_call.copy()
    df_cust_instm = df_cust_instm.merge(df_instm_g, on=['cst_keywrd'], how='left')
    df_cust_instm = df_cust_instm[((df_cust_instm.call_dt_x-df_cust_instm.call_dt_y)>=pd.Timedelta(0,'D')) & ((df_cust_instm.call_dt_x-df_cust_instm.call_dt_y)<pd.Timedelta(540,'D')) ]
    df_cust_instm = df_cust_instm.groupby(['cst_keywrd', 'call_dt_x']).max('call_dt_y').reset_index() 
    df_cust_instm.rename(columns={'call_dt_x' : 'call_dt'}, inplace=True)
    
    df_cust_instm_03 = df_call.copy()
    df_cust_instm_03 = df_cust_instm_03.merge(df_instm_03_g, on=['cst_keywrd'], how='left')
    df_cust_instm_03 = df_cust_instm_03[((df_cust_instm_03.call_dt_x-df_cust_instm_03.call_dt_y)>=pd.Timedelta(0,'D')) & ((df_cust_instm_03.call_dt_x-df_cust_instm_03.call_dt_y)<pd.Timedelta(540,'D')) ]
    df_cust_instm_03 = df_cust_instm_03.groupby(['cst_keywrd', 'call_dt_x']).max('call_dt_y').reset_index() 
    df_cust_instm_03.rename(columns={'call_dt_x' : 'call_dt'}, inplace=True)
    
    df_cust_instm_cat = df_call.copy()
    df_cust_instm_cat = df_cust_instm_cat.merge(df_instm_cat_g, on=['cst_keywrd'], how='left')
    df_cust_instm_cat = df_cust_instm_cat[((df_cust_instm_cat.call_dt_x-df_cust_instm_cat.call_dt_y)>=pd.Timedelta(0,'D')) & ((df_cust_instm_cat.call_dt_x-df_cust_instm_cat.call_dt_y)<pd.Timedelta(540,'D')) ]
    df_cust_instm_cat = df_cust_instm_cat.groupby(['cst_keywrd', 'call_dt_x']).max('call_dt_y').reset_index() 
    df_cust_instm_cat.rename(columns={'call_dt_x' : 'call_dt'}, inplace=True)    
     
    
#   训练数据和测试数据的时间范围，打标签
    df_cust = df_cust.merge(df_cust_call, on=['cst_keywrd', 'call_dt'], how='left')
    df_cust = df_cust.merge(df_cust_instm, on=['cst_keywrd', 'call_dt'], how='left')
    df_cust = df_cust.merge(df_cust_instm_03, on=['cst_keywrd', 'call_dt'], how='left')
    df_cust = df_cust.merge(df_cust_instm_cat, on=['cst_keywrd', 'call_dt'], how='left')
    
    
    df_cust['call_conn_rate'] = df_cust.apply(lambda x : x['call_conn_num'] / x['call_num']*100 if x['call_num'] != 0 else 0, axis=1)
    df_cust['instm_cat_rate'] = df_cust.apply(lambda x : x['instm_cat_num'] / x['instm_num']*100 if x['instm_num'] != 0 else 0, axis=1)
    df_cust['instm_03_rate'] = df_cust.apply(lambda x : x['instm_03_num'] / x['instm_num']*100 if x['instm_num'] != 0 else 0, axis=1)
    
    joblib.dump(df_cust, '/home/<USER>/work/fqt/cache/df_cust.pkl')
    
    return df_cust 


# In[103]:


def cr_csv(df, out_file):    
    
    df.to_csv('/home/<USER>/work/fqt/csv/%s' % out_file, index=False)
        
    df_s = df.sample(2000)
    df_s.to_csv('/home/<USER>/work/fqt/csv/%s' %  out_file[:out_file.rfind('.')]+'_s.csv', index=False)


# In[104]:


##################################
#
# 输入：
#     in_train：           训练数据
#     in_test:             当月外呼数据
#     calling_instm_file： 历史外呼和进件数据
#
# 输出：
#     out_train:           加好特征的训练数据
#     out_test：           加好特征的标签的数据
#     out_val：            加好特征的验证数据

# 控制参数：
#     cache_level：
#                         0：  全部重跑特征
#                         1：  使用instm缓存，跑特征
#                         2：  使用instm缓存、特征缓存，跑target encode
#     feat_ver:           特征版本号
#     is_train:           
#                         True:   仅生成out_train、out_val
#                         False:  同时生成out_train、out_val、out_test
##################################

def gen_data(in_train, in_test, calling_instm_file, out_train, out_test, out_val, feat_ver, train_date, cache_level=0, is_train=True):



    print(f"cre_calling_instm开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if cache_level <= 0:
        df_call_g, df_instm_03_g, df_instm_g, df_instm_cat_g = cre_calling_instm(calling_instm_file)

        joblib.dump(df_call_g, '/home/<USER>/work/fqt/cache/df_call_g.pkl')
        joblib.dump(df_instm_g, '/home/<USER>/work/fqt/cache/df_instm_g.pkl')
        joblib.dump(df_instm_03_g, '/home/<USER>/work/fqt/cache/df_instm_03_g.pkl')
        joblib.dump(df_instm_cat_g, '/home/<USER>/work/fqt/cache/df_instm_cat_g.pkl')
        
    print(f"cre_calling_instm结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")        

    print(f"cr_feature开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")        
    if cache_level <= 1:
        df_train = cr_feature(in_train)
        joblib.dump(df_train, '/home/<USER>/work/fqt/cache/df_train%s.pkl' % (feat_ver))

        if not is_train:
            df_test = cr_feature(in_test)        
            joblib.dump(df_test, '/home/<USER>/work/fqt/cache/df_test%s.pkl' % (feat_ver))      

    print(f"cr_feature结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")                    

    if cache_level == 2:      
        df_train = joblib.load('/home/<USER>/work/fqt/cache/df_train%s.pkl' % (feat_ver))
        
        if not is_train:
            df_test = joblib.load('/home/<USER>/work/fqt/cache/df_test%s.pkl' % (feat_ver))



    df_train['call_dt']=pd.to_datetime(df_train['call_dt'])
    
    cat_cols = [col for col in df_train.columns if col.find('_ind')!=-1 or col.find('_cd')!=-1 or col.find('_tpcd')!=-1]
    num_cols = [col for col in df_train.columns if col.find('_ind')==-1 and col.find('_cd')==-1 and col.find('_tpcd')==-1]
    
    print(cat_cols)
    pbar = tqdm(total=len(cat_cols))
        

    df_train['crt_insid_ind'] = df_train['crt_insid_ind'].apply(lambda x : pd.to_numeric(x, errors='coerce'))
    
    if not is_train:
        df_test['crt_insid_ind'] = df_test['crt_insid_ind'].apply(lambda x : pd.to_numeric(x, errors='coerce'))
        
    df_train['stmt_adr_zipecd_ind'] = df_train['stmt_adr_zipecd_ind'].apply(lambda x : pd.to_numeric(x, errors='coerce'))
    if not is_train:
        df_test['stmt_adr_zipecd_ind'] = df_test['stmt_adr_zipecd_ind'].apply(lambda x : pd.to_numeric(x, errors='coerce'))
        
        
#####设置项#################
#   避免使用未知数据  
###########################              
    enc_df_train = df_train[(df_train.call_dt>='2022-1-31') & (df_train.call_dt<=train_date)].copy()

    print(f"进入特征概率计算时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    N_min = 1
    feature_cols = []    
    
    for c in cat_cols:

        # fit encoder
        be = BetaEncoder(c)
        be.fit(enc_df_train, 'target')

        # mean
        feature_name = f'{c}_mean'
        df_train[c].fillna('1415926')
        df_train[feature_name] = be.transform(df_train, 'mean', N_min)
        
        pbar.set_description(f'{c}')
        pbar.update(1)
        
        if not is_train:
            df_test[c].fillna('1415926')
            df_test[feature_name]  = be.transform(df_test,  'mean', N_min)
        feature_cols.append(feature_name)

        
    train_target_enc = df_train[num_cols + feature_cols]


#####设置项#################
# 训练数据、验证数据分割日期
###########################
    train = train_target_enc[(train_target_enc.call_dt>='2022-2-28') & (train_target_enc.call_dt<=train_date)].copy()
    val = train_target_enc[train_target_enc.call_dt== train_date].copy()
    
    print(train.shape)

    if not is_train:
        test = df_test[num_cols + feature_cols]

    train.drop(['call_dt'], axis=1, inplace=True)
    val.drop(['call_dt'], axis=1, inplace=True)
    
    if not is_train:
        test.drop(['call_dt'], axis=1, inplace=True)        

    print("create train.csv")
    cr_csv(train, out_train)
    
    if not is_train:    
        print("create test.csv")
        cr_csv(test, out_test)
        
    print("create val.csv")
    cr_csv(val, out_val)
    

#######设置项###############
# 输入：
# 训练数据：train6_2205t2306xx.csv
# 测试数据：test6_0731.csv
# 进件特征数据：call_instm_20231205.csv
# 
#
# 输出：
# 训练数据：train6.3f_2207t2305.csv
# 测试数据：test6.3f_0731.csv
# 验证数据：val6.3f_0630.csv (由训练数据拆分出来)
#
# 特征版本：7.0_220731_230630l（版本号+数据日期）
# 特征数据类型：
#       仅生成训练数据：               is_train=True
#       同时生成训练数据、测试数据：    is_train=False
###########################
def run_fv(base_date):
    start_time = datetime.now()
    print(f"程序开始时间: {start_time}")

    train_date = base_date
    test_date = get_date(train_date, 1)

    gen_data(f'train8_{train_date}.csv', f'test8_{test_date}.csv', f'call_instm_{train_date}.csv', f'train202503f_{train_date}.csv', f'test202503f_{test_date}.csv', f'val202503f_{train_date}.csv', f'test202503_{train_date}', train_date, 0, is_train=False)

    end_time = datetime.now()
    print(f"程序结束时间: {end_time}")
    run_time = end_time - start_time
    print(f"程序运行时间: {run_time}")


# In[105]:


if __name__ == "__main__":
    if is_jupyter():
        base_date = input('输入日期：')
    else:
        if len(sys.argv) != 2:
            print(f"Usage: {sys.argv[0]} YYYYMMDD")
            sys.exit(1)
        base_date = sys.argv[1]
        
    run_fv(base_date)

