#!/usr/bin/env python
# coding: utf-8

"""
智能特征选择器
用于替代硬编码的特征删除，通过多种策略智能选择最有价值的特征
预期AUC提升：0.02-0.04
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Tuple, Optional, Set
from sklearn.feature_selection import (
    SelectKBest, f_classif, mutual_info_classif,
    RFE, RFECV, SelectFromModel
)
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
import lightgbm as lgb
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class IntelligentFeatureSelector:
    """
    智能特征选择器
    
    使用多种策略选择最有价值的特征：
    1. 统计方法（方差、相关性）
    2. 单变量选择（F检验、互信息）
    3. 模型重要性选择
    4. 递归特征消除
    5. 稳定性选择
    """
    
    def __init__(self, target_features: int = None, correlation_threshold: float = 0.95,
                 variance_threshold: float = 0.01, random_state: int = 42):
        """
        初始化特征选择器
        
        Args:
            target_features: 目标特征数量
            correlation_threshold: 相关性阈值
            variance_threshold: 方差阈值
            random_state: 随机种子
        """
        self.target_features = target_features
        self.correlation_threshold = correlation_threshold
        self.variance_threshold = variance_threshold
        self.random_state = random_state
        
        # 存储选择结果
        self.selected_features = None
        self.feature_scores = {}
        self.selection_history = {}
        
    def fit_select(self, X: pd.DataFrame, y: pd.Series) -> List[str]:
        """
        拟合并选择特征
        
        Args:
            X: 特征数据
            y: 目标变量
            
        Returns:
            选择的特征列表
        """
        logger.info("开始智能特征选择...")
        logger.info(f"原始特征数量: {X.shape[1]}")
        
        # 保存原始特征
        original_features = X.columns.tolist()
        
        # 步骤1: 基础过滤
        X_filtered = self._basic_filtering(X, y)
        logger.info(f"基础过滤后特征数量: {X_filtered.shape[1]}")
        
        # 步骤2: 相关性过滤
        X_corr_filtered = self._correlation_filtering(X_filtered)
        logger.info(f"相关性过滤后特征数量: {X_corr_filtered.shape[1]}")
        
        # 步骤3: 统计方法选择
        stat_features = self._statistical_selection(X_corr_filtered, y)
        
        # 步骤4: 模型重要性选择
        importance_features = self._importance_based_selection(X_corr_filtered, y)
        
        # 步骤5: 递归特征消除
        rfe_features = self._recursive_feature_elimination(X_corr_filtered, y)
        
        # 步骤6: 稳定性选择
        stable_features = self._stability_selection(X_corr_filtered, y)
        
        # 步骤7: 集成选择结果
        final_features = self._ensemble_selection(
            X_corr_filtered.columns.tolist(),
            stat_features, importance_features, rfe_features, stable_features
        )
        
        self.selected_features = final_features
        
        logger.info(f"最终选择特征数量: {len(final_features)}")
        logger.info(f"特征选择完成，保留比例: {len(final_features)/len(original_features):.2%}")
        
        return final_features
    
    def _basic_filtering(self, X: pd.DataFrame, y: pd.Series) -> pd.DataFrame:
        """基础过滤：去除低方差和常数特征"""
        # 去除常数特征
        constant_features = [col for col in X.columns if X[col].nunique() <= 1]
        if constant_features:
            logger.info(f"删除常数特征: {len(constant_features)} 个")
            X = X.drop(constant_features, axis=1)
        
        # 去除低方差特征
        numeric_cols = X.select_dtypes(include=[np.number]).columns
        low_variance_features = []
        
        for col in numeric_cols:
            if X[col].var() < self.variance_threshold:
                low_variance_features.append(col)
        
        if low_variance_features:
            logger.info(f"删除低方差特征: {len(low_variance_features)} 个")
            X = X.drop(low_variance_features, axis=1)
        
        self.selection_history['basic_filtering'] = {
            'removed_constant': constant_features,
            'removed_low_variance': low_variance_features,
            'remaining': X.columns.tolist()
        }
        
        return X
    
    def _correlation_filtering(self, X: pd.DataFrame) -> pd.DataFrame:
        """相关性过滤：去除高度相关的特征"""
        # 计算相关性矩阵
        corr_matrix = X.corr().abs()
        
        # 找到高度相关的特征对
        upper_triangle = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )
        
        # 选择要删除的特征
        to_drop = [column for column in upper_triangle.columns 
                  if any(upper_triangle[column] > self.correlation_threshold)]
        
        if to_drop:
            logger.info(f"删除高相关特征: {len(to_drop)} 个")
            X = X.drop(to_drop, axis=1)
        
        self.selection_history['correlation_filtering'] = {
            'removed': to_drop,
            'threshold': self.correlation_threshold,
            'remaining': X.columns.tolist()
        }
        
        return X
    
    def _statistical_selection(self, X: pd.DataFrame, y: pd.Series) -> List[str]:
        """统计方法选择特征"""
        try:
            # 准备数据
            X_numeric = X.select_dtypes(include=[np.number]).fillna(0)
            
            # F检验选择
            k_best = min(len(X_numeric.columns), self.target_features or len(X_numeric.columns)//2)
            f_selector = SelectKBest(score_func=f_classif, k=k_best)
            f_selector.fit(X_numeric, y)
            f_features = X_numeric.columns[f_selector.get_support()].tolist()
            
            # 互信息选择
            mi_selector = SelectKBest(score_func=mutual_info_classif, k=k_best)
            mi_selector.fit(X_numeric, y)
            mi_features = X_numeric.columns[mi_selector.get_support()].tolist()
            
            # 合并结果
            stat_features = list(set(f_features + mi_features))
            
            # 保存分数
            self.feature_scores['f_test'] = dict(zip(X_numeric.columns, f_selector.scores_))
            self.feature_scores['mutual_info'] = dict(zip(X_numeric.columns, mi_selector.scores_))
            
            logger.info(f"统计方法选择特征: {len(stat_features)} 个")
            return stat_features
            
        except Exception as e:
            logger.warning(f"统计方法选择失败: {e}")
            return X.columns.tolist()
    
    def _importance_based_selection(self, X: pd.DataFrame, y: pd.Series) -> List[str]:
        """基于模型重要性选择特征"""
        try:
            # 准备数据
            X_numeric = X.select_dtypes(include=[np.number]).fillna(0)
            
            # 随机森林重要性
            rf = RandomForestClassifier(n_estimators=100, random_state=self.random_state, n_jobs=-1)
            rf.fit(X_numeric, y)
            rf_importance = rf.feature_importances_
            
            # LightGBM重要性
            lgb_model = lgb.LGBMClassifier(
                n_estimators=100, random_state=self.random_state, 
                verbose=-1, n_jobs=-1
            )
            lgb_model.fit(X_numeric, y)
            lgb_importance = lgb_model.feature_importances_
            
            # 综合重要性分数
            combined_importance = (rf_importance + lgb_importance) / 2
            
            # 选择重要性最高的特征
            n_features = min(len(X_numeric.columns), self.target_features or len(X_numeric.columns)//2)
            top_indices = np.argsort(combined_importance)[-n_features:]
            importance_features = X_numeric.columns[top_indices].tolist()
            
            # 保存重要性分数
            self.feature_scores['rf_importance'] = dict(zip(X_numeric.columns, rf_importance))
            self.feature_scores['lgb_importance'] = dict(zip(X_numeric.columns, lgb_importance))
            self.feature_scores['combined_importance'] = dict(zip(X_numeric.columns, combined_importance))
            
            logger.info(f"重要性方法选择特征: {len(importance_features)} 个")
            return importance_features
            
        except Exception as e:
            logger.warning(f"重要性方法选择失败: {e}")
            return X.columns.tolist()
    
    def _recursive_feature_elimination(self, X: pd.DataFrame, y: pd.Series) -> List[str]:
        """递归特征消除"""
        try:
            # 准备数据
            X_numeric = X.select_dtypes(include=[np.number]).fillna(0)
            
            # 使用逻辑回归进行RFE
            estimator = LogisticRegression(random_state=self.random_state, max_iter=1000)
            n_features = min(len(X_numeric.columns), self.target_features or len(X_numeric.columns)//2)
            
            rfe = RFE(estimator=estimator, n_features_to_select=n_features)
            rfe.fit(X_numeric, y)
            
            rfe_features = X_numeric.columns[rfe.support_].tolist()
            
            # 保存排名
            self.feature_scores['rfe_ranking'] = dict(zip(X_numeric.columns, rfe.ranking_))
            
            logger.info(f"RFE选择特征: {len(rfe_features)} 个")
            return rfe_features
            
        except Exception as e:
            logger.warning(f"RFE选择失败: {e}")
            return X.columns.tolist()
    
    def _stability_selection(self, X: pd.DataFrame, y: pd.Series, 
                           n_bootstrap: int = 50, threshold: float = 0.6) -> List[str]:
        """稳定性选择"""
        try:
            # 准备数据
            X_numeric = X.select_dtypes(include=[np.number]).fillna(0)
            
            feature_selection_counts = np.zeros(len(X_numeric.columns))
            
            # Bootstrap采样和特征选择
            for i in range(n_bootstrap):
                # 随机采样
                sample_indices = np.random.choice(
                    len(X_numeric), size=int(0.8 * len(X_numeric)), replace=False
                )
                X_sample = X_numeric.iloc[sample_indices]
                y_sample = y.iloc[sample_indices]
                
                # 使用LightGBM选择特征
                lgb_model = lgb.LGBMClassifier(
                    n_estimators=50, random_state=self.random_state + i,
                    verbose=-1, n_jobs=1
                )
                lgb_model.fit(X_sample, y_sample)
                
                # 选择重要性最高的一半特征
                importance = lgb_model.feature_importances_
                n_select = len(X_numeric.columns) // 2
                top_indices = np.argsort(importance)[-n_select:]
                
                feature_selection_counts[top_indices] += 1
            
            # 计算选择频率
            selection_frequency = feature_selection_counts / n_bootstrap
            
            # 选择频率高于阈值的特征
            stable_indices = np.where(selection_frequency >= threshold)[0]
            stable_features = X_numeric.columns[stable_indices].tolist()
            
            # 保存选择频率
            self.feature_scores['stability_frequency'] = dict(zip(X_numeric.columns, selection_frequency))
            
            logger.info(f"稳定性选择特征: {len(stable_features)} 个")
            return stable_features
            
        except Exception as e:
            logger.warning(f"稳定性选择失败: {e}")
            return X.columns.tolist()
    
    def _ensemble_selection(self, all_features: List[str], *feature_lists: List[str]) -> List[str]:
        """集成多种选择方法的结果"""
        # 计算每个特征被选择的次数
        feature_votes = {}
        for features in feature_lists:
            for feature in features:
                if feature in all_features:
                    feature_votes[feature] = feature_votes.get(feature, 0) + 1
        
        # 按投票数排序
        sorted_features = sorted(feature_votes.items(), key=lambda x: x[1], reverse=True)
        
        # 选择投票数最高的特征
        if self.target_features:
            final_features = [f for f, _ in sorted_features[:self.target_features]]
        else:
            # 选择至少被2种方法选中的特征
            final_features = [f for f, votes in sorted_features if votes >= 2]
        
        # 保存投票结果
        self.feature_scores['ensemble_votes'] = feature_votes
        
        return final_features
    
    def get_feature_importance_report(self) -> pd.DataFrame:
        """获取特征重要性报告"""
        if not self.feature_scores:
            return pd.DataFrame()
        
        # 合并所有分数
        all_features = set()
        for scores in self.feature_scores.values():
            all_features.update(scores.keys())
        
        report_data = []
        for feature in all_features:
            row = {'feature': feature}
            for method, scores in self.feature_scores.items():
                row[method] = scores.get(feature, 0)
            row['selected'] = feature in (self.selected_features or [])
            report_data.append(row)
        
        return pd.DataFrame(report_data).sort_values('combined_importance', ascending=False)
    
    def save_selection_report(self, filepath: str) -> None:
        """保存特征选择报告"""
        report = self.get_feature_importance_report()
        report.to_csv(filepath, index=False)
        logger.info(f"特征选择报告已保存: {filepath}")


def apply_intelligent_feature_selection(X: pd.DataFrame, y: pd.Series, 
                                       target_features: int = None) -> Tuple[pd.DataFrame, List[str]]:
    """
    应用智能特征选择
    
    Args:
        X: 特征数据
        y: 目标变量
        target_features: 目标特征数量
        
    Returns:
        选择后的特征数据和特征列表
    """
    selector = IntelligentFeatureSelector(target_features=target_features)
    selected_features = selector.fit_select(X, y)
    
    # 保存选择报告
    selector.save_selection_report('feature_selection_report.csv')
    
    return X[selected_features], selected_features
