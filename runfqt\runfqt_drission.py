import time
from DrissionPage import WebPage

page = WebPage()    

def login():
    page.get("http://ai.jh")  
    page.ele('xpath://a[contains(text(), "众研工作台")]').click()

    try:

        login_name = page.ele('xpath://input[@placeholder="请输入UASS用户名"]')

        login_name.input("houwei.jx") 

        if login_name:
            page.ele('xpath://input[@placeholder="请输入UASS密码"]').input("Jx86848216") 
            page.ele('xpath://*/form/div[4]/div/button').click()  
        
    except Exception as e:
        print(e)
        print('\n'*5)

    return page



def make_request(acquireId):
    url = f"http://ai.jh/v1/data/acquire/task/run"  
    
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Project-Id": "1119"
    }
    
    params = {
        "userId": "houwei.jx",
        "acquireId": acquireId
    }

    try:
        page.get(url, headers=headers, params=params)

        r = page.json

        if r["code"] == 200:
            print(f" SUCCESS：{acquireId}: {r}")
        else:
            print(f"ERROR: {acquireId}: {r}")
    except Exception as e:
        print(f"EXCEPTION：{acquireId}: {e}")

    if r["code"] == 200:
        return acquireId
    else:
        return 0



def run_data(timeout):
    acquireIds = {'3089': 'test', '2214': 'call_instm', '1862': 'train'}
                
    login()
    page.change_mode(mode='s')
    page.cookies_to_session()


    while acquireIds:
        next_run_id = {}
        for id, name in acquireIds.items():
            success_id = make_request(id)
            if not success_id:
                next_run_id[id] = name
            
            time.sleep(timeout)

        acquireIds = next_run_id.copy()
        



if __name__ == "__main__":

    timeout = 60
    run_data(timeout)
    page.quit()