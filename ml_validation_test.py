#!/usr/bin/env python
# coding: utf-8

"""
机器学习程序验证测试脚本
验证优化后的程序功能是否正确，并对比原程序的问题
"""

import pandas as pd
import numpy as np
import logging
import sys
from pathlib import Path
from datetime import datetime
import tempfile
import shutil

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_data(n_samples: int = 1000) -> tuple:
    """
    创建测试数据
    
    Args:
        n_samples: 样本数量
        
    Returns:
        训练和测试数据框
    """
    logger.info(f"创建测试数据: {n_samples} 样本")
    
    np.random.seed(42)
    
    # 创建训练数据
    train_data = {
        'cst_keywrd': [f'CUST_{i:06d}' for i in range(n_samples)],
        'target': np.random.binomial(1, 0.1, n_samples),
        'feature_1': np.random.normal(0, 1, n_samples),
        'feature_2': np.random.normal(0, 1, n_samples),
        'feature_3': np.random.randint(0, 10, n_samples),
        'feature_4': np.random.choice(['A', 'B', 'C'], n_samples),
    }
    
    # 添加一些需要删除的特征（在drop_columns列表中）
    train_data['bld_fnd_cst_ind_mean'] = np.random.random(n_samples)
    train_data['preex_btch_cst_ind_mean'] = np.random.random(n_samples)
    
    # 添加更多特征
    for i in range(10):
        train_data[f'numeric_feature_{i}'] = np.random.normal(0, 1, n_samples)
        train_data[f'categorical_feature_{i}'] = np.random.choice(['X', 'Y', 'Z'], n_samples)
    
    df_train = pd.DataFrame(train_data)
    
    # 创建测试数据（不包含target）
    test_data = train_data.copy()
    del test_data['target']
    test_data['cst_keywrd'] = [f'TEST_{i:06d}' for i in range(n_samples)]
    
    df_test = pd.DataFrame(test_data)
    
    logger.info(f"测试数据创建完成 - 训练: {df_train.shape}, 测试: {df_test.shape}")
    return df_train, df_test


def test_original_bugs():
    """测试原程序中的bug"""
    logger.info("测试原程序中的bug...")
    
    bugs_found = []
    
    # Bug 1: 文件路径格式错误
    try:
        # 模拟原程序第366行的错误
        model_path = '/tmp/'
        file_path = model_path + '%s_%s.json.txt'  # 缺少参数
        # 这会导致字符串格式化错误
        logger.info("Bug 1: 文件路径格式错误 - 已识别")
        bugs_found.append("文件路径格式错误")
    except:
        pass
    
    # Bug 2: GridSearchCV参数错误
    try:
        from sklearn.model_selection import GridSearchCV
        from sklearn.ensemble import RandomForestClassifier
        
        # 模拟原程序的错误用法
        model = RandomForestClassifier()
        param_grid = {'n_estimators': [10, 20]}
        
        grid = GridSearchCV(model, param_grid, refit=False)
        # 原程序设置refit=False但后续假设模型已训练
        logger.info("Bug 2: GridSearchCV refit=False 问题 - 已识别")
        bugs_found.append("GridSearchCV使用错误")
    except:
        pass
    
    # Bug 3: 文件命名不一致
    save_file = 'model.lgb.json'
    load_file = 'model.json.txt'
    if save_file != load_file:
        logger.info("Bug 3: 文件命名不一致 - 已识别")
        bugs_found.append("文件命名不一致")
    
    logger.info(f"原程序bug检测完成，发现 {len(bugs_found)} 个问题:")
    for i, bug in enumerate(bugs_found, 1):
        logger.info(f"  {i}. {bug}")
    
    return bugs_found


def test_optimized_features():
    """测试优化后的功能"""
    logger.info("测试优化后的功能...")
    
    try:
        from f202503m4_8_optimized import (
            ModelConfig, OptimizedModelTrainer, 
            optimize_data_types, drop_columns, get_date
        )
        
        # 测试配置类
        config = ModelConfig()
        assert hasattr(config, 'lgb_params'), "配置类缺少lgb_params"
        assert hasattr(config, 'param_grid'), "配置类缺少param_grid"
        logger.info("✅ 配置类测试通过")
        
        # 测试日期函数
        result_date = get_date('20240228', 1)
        expected_date = '20240331'
        assert result_date == expected_date, f"日期计算错误: {result_date} != {expected_date}"
        logger.info("✅ 日期函数测试通过")
        
        # 测试数据类型优化
        test_df = pd.DataFrame({
            'int_col': np.random.randint(0, 100, 100),
            'float_col': np.random.random(100),
            'large_int': np.random.randint(0, 1000000, 100)
        })
        
        original_memory = test_df.memory_usage(deep=True).sum()
        optimized_df = optimize_data_types(test_df.copy())
        optimized_memory = optimized_df.memory_usage(deep=True).sum()
        
        assert optimized_memory <= original_memory, "内存优化失败"
        logger.info("✅ 数据类型优化测试通过")
        
        # 测试特征删除
        test_df_with_drop_cols = pd.DataFrame({
            'keep_col': [1, 2, 3],
            'bld_fnd_cst_ind_mean': [1, 2, 3],  # 应该被删除
            'preex_btch_cst_ind_mean': [1, 2, 3]  # 应该被删除
        })
        
        result_df = drop_columns(test_df_with_drop_cols.copy())
        assert 'keep_col' in result_df.columns, "保留列被错误删除"
        assert 'bld_fnd_cst_ind_mean' not in result_df.columns, "删除列未被删除"
        logger.info("✅ 特征删除测试通过")
        
        logger.info("所有优化功能测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"优化功能测试失败: {e}")
        return False


def test_model_trainer():
    """测试模型训练器"""
    logger.info("测试模型训练器...")
    
    try:
        from f202503m4_8_optimized import ModelConfig, OptimizedModelTrainer
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 修改配置使用临时目录
            config = ModelConfig()
            config.data_path = temp_dir
            config.model_path = temp_dir
            
            # 创建测试数据
            df_train, df_test = create_test_data(100)
            
            # 保存测试数据
            train_file = Path(temp_dir) / 'test_train.csv'
            test_file = Path(temp_dir) / 'test_test.csv'
            
            df_train.to_csv(train_file, index=False)
            df_test.to_csv(test_file, index=False)
            
            # 初始化训练器
            trainer = OptimizedModelTrainer(config)
            
            # 测试数据加载
            loaded_train, loaded_test = trainer.load_data('test_train.csv', 'test_test.csv')
            assert loaded_train.shape == df_train.shape, "训练数据加载失败"
            assert loaded_test.shape == df_test.shape, "测试数据加载失败"
            logger.info("✅ 数据加载测试通过")
            
            # 测试数据预处理
            processed_train, processed_test = trainer.preprocess_data(loaded_train, loaded_test)
            assert processed_train.shape[0] == df_train.shape[0], "预处理后行数变化"
            logger.info("✅ 数据预处理测试通过")
            
            # 测试模型训练（使用小数据集和简单参数）
            config.param_grid = {
                'num_leaves': [10],
                'learning_rate': [0.1],
                'n_estimators': [10]
            }
            config.cv_folds = 2
            
            trainer.config = config
            train_results = trainer.train_model(processed_train)
            
            assert 'train_auc' in train_results, "训练结果缺少AUC指标"
            assert 'val_auc' in train_results, "训练结果缺少验证AUC指标"
            logger.info("✅ 模型训练测试通过")
            
            # 测试模型保存和加载
            trainer.save_model('test', 'v1')
            
            # 创建新的训练器并加载模型
            new_trainer = OptimizedModelTrainer(config)
            new_trainer.load_model('test', 'v1')
            
            assert new_trainer.model is not None, "模型加载失败"
            logger.info("✅ 模型保存和加载测试通过")
            
            # 测试预测
            predictions = trainer.predict(processed_test)
            assert len(predictions) == len(processed_test), "预测结果长度不匹配"
            assert np.all((predictions >= 0) & (predictions <= 1)), "预测概率超出[0,1]范围"
            logger.info("✅ 模型预测测试通过")
        
        logger.info("模型训练器所有测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"模型训练器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_validation_tests():
    """运行所有验证测试"""
    logger.info("开始运行机器学习程序验证测试...")
    
    test_results = {}
    
    # 测试原程序bug
    logger.info("=" * 50)
    bugs_found = test_original_bugs()
    test_results['bugs_identified'] = len(bugs_found)
    
    # 测试优化功能
    logger.info("=" * 50)
    optimized_test_passed = test_optimized_features()
    test_results['optimized_features'] = optimized_test_passed
    
    # 测试模型训练器
    logger.info("=" * 50)
    trainer_test_passed = test_model_trainer()
    test_results['model_trainer'] = trainer_test_passed
    
    # 输出总结
    logger.info("=" * 50)
    logger.info("验证测试总结:")
    logger.info(f"  原程序bug识别: {test_results['bugs_identified']} 个")
    logger.info(f"  优化功能测试: {'通过' if test_results['optimized_features'] else '失败'}")
    logger.info(f"  模型训练器测试: {'通过' if test_results['model_trainer'] else '失败'}")
    
    all_passed = (
        test_results['bugs_identified'] > 0 and
        test_results['optimized_features'] and
        test_results['model_trainer']
    )
    
    if all_passed:
        logger.info("🎉 所有验证测试通过！优化程序工作正常。")
        return True
    else:
        logger.warning("⚠️ 部分测试未通过，请检查代码。")
        return False


if __name__ == "__main__":
    success = run_validation_tests()
    sys.exit(0 if success else 1)
