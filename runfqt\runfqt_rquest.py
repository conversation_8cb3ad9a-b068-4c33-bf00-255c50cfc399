import requests
import time

def make_request(acquireId):
    url = f"http://ai.jh/v1/data/acquire/task/run"  
    
    headers = {
        "Accept": "application/json, text/plain, */*",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36",
        "Accept-Encoding": "gzip, deflate",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Project-Id": "1119",
        "Cookie": "areaFlag=private; exitUrl=/sso/logoutPrivate; tgw_l7_route=3e0fa4f24fad4da15d5c35bae9ffcada; ai_platform_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0eXBlIjoiYWNjZXNzX3Rva2VuIiwiZXhwIjoxNzI2MTE5NDU5LCJ1c2VybmFtZSI6ImhvdXdlaS5qeCJ9.w4d4bNOd9Bt-helBSFEsa5bcManykQg0n47BPbalSzk; ai_platform_username=houwei.jx; refresh_token=da84b2986943fc21b150f24e994a96f06566595e777d4ef13b66c6afa0954070bebdcd7ab1ed49ae2e2d42c4358199160ed6ec84580a24c6b7fa704ebde6a7697fb6e42aa280109b941cf1dfbf9b24b7a4d541b799ee564891a0d0a78bcd119176b710489fec45da2dae551c8225bd219ef720ea8ef3bc85e84365c3b814932e8f7fc33002c5baf380662c037d03faaf2129f42893184646e7716d5e28b6c5763435e03a469601efc0f7fb52b3a367ae; project_id=1119; zc_did=%7B%22did%22%3A%20%22191e39f442817-022a5dadd5917-5f4d2e12-1fa400-191e39f4429613%22%7D; zc_tspcwxk24yg1f0ld=%7B%22sid%22%3A%20%221726108497394_270296585295831%22%2C%22updated%22%3A%201726117303011%2C%22info%22%3A%201726100751405%2C%22superProperty%22%3A%20%22%7B%5C%22app_id%5C%22%3A%20%5C%22tspcwxk24yg1f0ld%5C%22%2C%5C%22APP_NAME%5C%22%3A%20%5C%22%E7%BB%9F%E4%B8%80%E4%BE%9B%E7%BB%99%E9%97%A8%E6%88%B7%5C%22%7D%22%2C%22platform%22%3A%20%22%7B%7D%22%2C%22utm%22%3A%20%22%7B%7D%22%2C%22referrerDomain%22%3A%20%22%22%7D"  
    }
    
    params = {
        "userId": "houwei.jx",
        "acquireId": acquireId
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            print(f" (acquireId={acquireId}): {response.status_code}: {response.text}")
        elif response.status_code == 500:
            print(f" (acquireId={acquireId}): {response.status_code}: {response.text}")
        else:
            print(f" (acquireId={acquireId}): {response.status_code}: {response.text}")
    except requests.RequestException as e:
        print(f" (acquireId={acquireId}): {e}")


def run_data(timeout):
    acquireIds = [  
                    3089,     #test
                    2214,     #call_instm
                    1862      #train
                ]
    for acquireId in acquireIds:
        make_request(acquireId)
        time.sleep(timeout)

if __name__ == "__main__":
    timeout = 180
    run_data(timeout)