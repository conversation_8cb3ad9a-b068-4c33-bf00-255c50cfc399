import subprocess
import csv
import os
import sys
from datetime import datetime

import concurrent.futures
import tempfile

def execute_beeline_command(query):
    with tempfile.NamedTemporaryFile(mode='w+t', delete=False) as temp_file:
        try:
            command = f'beeline --verbose=true --outputformat=csv2 -e "{query}" > {temp_file.name}'
            subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
            
            return temp_file.name

        except subprocess.CalledProcessError as e:
            os.unlink(temp_file.name)
            print(f"beeline错误返回码: {e.returncode}")
            print(f"Command output:\n{e.output}")
            print(f"Error output:\n{e.stderr}")
            raise
        except Exception as e:
            os.unlink(temp_file.name)
            print(f"Unexpected error: {e}")
            raise


def process_file(file_path, output_file):
    with open(file_path, 'r') as input_file, open(output_file, 'w', newline='') as output_csv:
        csv_writer = csv.writer(output_csv)
        
        for _ in range(4):
            next(input_file)
        
        for line in input_file:
            csv_writer.writerow(line.strip().split(','))

def get_date(base_date, months_to_add):
    base_date = datetime.strptime(base_date, "%Y%m%d")
    year = base_date.year
    month = base_date.month + months_to_add
    
    year += (month - 1) // 12
    month = (month - 1) % 12 + 1
    
    last_day = calendar.monthrange(year, month)[1]
    
    return datetime(year, month, last_day).strftime("%Y%m%d")


def process_query(args):
    query, prefix, months_to_add, base_date, output_dir = args
    try:
        temp_file_path = execute_beeline_command(query)
        filename = f"{prefix}_{get_date(base_date, months_to_add)}.csv"
        output_file = os.path.join(output_dir, filename)
        os.makedirs(output_dir, exist_ok=True)
        process_file(temp_file_path, output_file)
        return f"Saved {filename}"
    except subprocess.CalledProcessError:
        return f"Failed to process query for {prefix}"
    finally:
        if 'temp_file_path' in locals():
            os.unlink(temp_file_path)        

def con_getcsv(base_date):

    output_dir = '/home/<USER>/work/fqt/csv'
    
    queries = [
        ("select * from db_1119_bd_platform.dim_jx_trans", "train8", 0),
        ("select * from db_1119_bd_platform.dim_jx_trans_test", "test8", 1),
        ("select * from db_1119_bd_platform.dim_jx_trans_var", "call_instm", 0)
    ]

    print('进入con_getcsv...')
    with concurrent.futures.ProcessPoolExecutor() as executor:
        futures = [executor.submit(process_query, (query, prefix, months_to_add, base_date, output_dir)) 
                   for query, prefix, months_to_add in queries]
        
        for future in concurrent.futures.as_completed(futures):
            print(future.result())

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(f"Usage: {sys.argv[0]} YYYYMMDD")
        sys.exit(1)
    
    base_date = sys.argv[1]
    con_getcsv(base_date)                  

