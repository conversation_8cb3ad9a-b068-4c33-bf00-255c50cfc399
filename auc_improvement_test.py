#!/usr/bin/env python
# coding: utf-8

"""
AUC提升效果测试脚本
对比优化前后的AUC提升效果
"""

import pandas as pd
import numpy as np
import logging
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score, classification_report
from sklearn.ensemble import RandomForestClassifier
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_synthetic_data(n_samples: int = 10000, n_features: int = 100, 
                         imbalance_ratio: float = 0.1) -> tuple:
    """
    创建合成数据集，模拟真实的特征工程数据
    
    Args:
        n_samples: 样本数量
        n_features: 特征数量
        imbalance_ratio: 正样本比例
        
    Returns:
        特征数据和标签
    """
    logger.info(f"创建合成数据集: {n_samples} 样本, {n_features} 特征")
    
    np.random.seed(42)
    
    # 创建特征数据
    X = np.random.randn(n_samples, n_features)
    
    # 添加一些有意义的特征
    # 特征1-10: 与目标强相关
    for i in range(10):
        X[:, i] = X[:, i] + np.random.randn(n_samples) * 0.5
    
    # 特征11-30: 中等相关
    for i in range(10, 30):
        X[:, i] = X[:, i] + np.random.randn(n_samples) * 1.0
    
    # 特征31-100: 噪声特征
    for i in range(30, n_features):
        X[:, i] = np.random.randn(n_samples)
    
    # 创建目标变量
    # 基于前30个特征创建目标
    linear_combination = (
        2 * X[:, 0] + 1.5 * X[:, 1] + 1.2 * X[:, 2] + 
        0.8 * X[:, 5] + 0.6 * X[:, 8] +
        0.4 * np.sum(X[:, 10:20], axis=1) +
        0.2 * np.sum(X[:, 20:30], axis=1)
    )
    
    # 添加非线性关系
    nonlinear_term = 0.3 * X[:, 0] * X[:, 1] + 0.2 * np.sin(X[:, 2])
    
    # 计算概率
    logits = linear_combination + nonlinear_term
    probabilities = 1 / (1 + np.exp(-logits))
    
    # 生成标签，确保指定的不平衡比例
    n_positive = int(n_samples * imbalance_ratio)
    y = np.zeros(n_samples)
    
    # 选择概率最高的样本作为正样本
    positive_indices = np.argsort(probabilities)[-n_positive:]
    y[positive_indices] = 1
    
    # 转换为DataFrame
    feature_names = [f'feature_{i}' for i in range(n_features)]
    X_df = pd.DataFrame(X, columns=feature_names)
    y_series = pd.Series(y, name='target')
    
    logger.info(f"数据创建完成 - 正样本比例: {y.mean():.3f}")
    
    return X_df, y_series


def test_baseline_model(X_train, y_train, X_test, y_test):
    """测试基线模型（简单LightGBM）"""
    logger.info("测试基线模型...")
    
    # 简单的LightGBM模型
    model = lgb.LGBMClassifier(
        n_estimators=100,
        learning_rate=0.1,
        random_state=42,
        verbose=-1
    )
    
    model.fit(X_train, y_train)
    y_pred_proba = model.predict_proba(X_test)[:, 1]
    baseline_auc = roc_auc_score(y_test, y_pred_proba)
    
    logger.info(f"基线模型AUC: {baseline_auc:.4f}")
    return baseline_auc


def test_feature_selection_improvement(X_train, y_train, X_test, y_test):
    """测试智能特征选择的改进效果"""
    logger.info("测试智能特征选择改进...")
    
    try:
        from intelligent_feature_selector import apply_intelligent_feature_selection
        
        # 应用智能特征选择
        X_train_selected, selected_features = apply_intelligent_feature_selection(
            X_train, y_train, target_features=50
        )
        X_test_selected = X_test[selected_features]
        
        # 训练模型
        model = lgb.LGBMClassifier(
            n_estimators=100,
            learning_rate=0.1,
            random_state=42,
            verbose=-1
        )
        
        model.fit(X_train_selected, y_train)
        y_pred_proba = model.predict_proba(X_test_selected)[:, 1]
        feature_selection_auc = roc_auc_score(y_test, y_pred_proba)
        
        logger.info(f"特征选择后AUC: {feature_selection_auc:.4f}")
        logger.info(f"选择特征数: {len(selected_features)}/{len(X_train.columns)}")
        
        return feature_selection_auc
        
    except ImportError:
        logger.warning("智能特征选择器不可用")
        return None


def test_ensemble_improvement(X_train, y_train, X_test, y_test):
    """测试模型集成的改进效果"""
    logger.info("测试模型集成改进...")
    
    try:
        from enhanced_model_trainer import EnhancedModelTrainer
        from f202509m4_9 import ModelConfig
        
        # 创建配置
        config = ModelConfig()
        config.test_size = 0.2
        config.random_state = 42
        
        # 准备数据
        df_train = X_train.copy()
        df_train['target'] = y_train
        df_train['cst_keywrd'] = [f'CUST_{i}' for i in range(len(df_train))]
        
        # 创建增强训练器
        trainer = EnhancedModelTrainer(config)
        
        # 训练集成模型
        train_results = trainer.train_ensemble_model(df_train)
        
        # 预测
        df_test = X_test.copy()
        df_test['cst_keywrd'] = [f'TEST_{i}' for i in range(len(df_test))]
        
        y_pred_proba = trainer.predict(df_test)
        ensemble_auc = roc_auc_score(y_test, y_pred_proba)
        
        logger.info(f"集成模型AUC: {ensemble_auc:.4f}")
        logger.info(f"验证集AUC: {train_results['val_auc']:.4f}")
        
        return ensemble_auc
        
    except ImportError:
        logger.warning("增强模型训练器不可用")
        return None


def test_hyperparameter_optimization(X_train, y_train, X_test, y_test):
    """测试超参数优化的改进效果"""
    logger.info("测试超参数优化改进...")
    
    from sklearn.model_selection import GridSearchCV
    
    # 扩展的超参数搜索
    param_grid = {
        'n_estimators': [200, 500],
        'learning_rate': [0.05, 0.1],
        'num_leaves': [31, 63],
        'feature_fraction': [0.8, 0.9],
        'bagging_fraction': [0.8, 0.9]
    }
    
    model = lgb.LGBMClassifier(random_state=42, verbose=-1)
    
    grid_search = GridSearchCV(
        estimator=model,
        param_grid=param_grid,
        scoring='roc_auc',
        cv=3,
        n_jobs=-1
    )
    
    grid_search.fit(X_train, y_train)
    
    y_pred_proba = grid_search.predict_proba(X_test)[:, 1]
    optimized_auc = roc_auc_score(y_test, y_pred_proba)
    
    logger.info(f"超参数优化后AUC: {optimized_auc:.4f}")
    logger.info(f"最佳参数: {grid_search.best_params_}")
    
    return optimized_auc


def run_auc_improvement_tests():
    """运行AUC提升测试"""
    logger.info("开始AUC提升效果测试...")
    
    # 创建测试数据
    X, y = create_synthetic_data(n_samples=5000, n_features=80, imbalance_ratio=0.08)
    
    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, stratify=y, random_state=42
    )
    
    logger.info(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    logger.info(f"训练集正样本比例: {y_train.mean():.3f}")
    logger.info(f"测试集正样本比例: {y_test.mean():.3f}")
    
    # 测试结果
    results = {}
    
    # 1. 基线模型
    results['baseline'] = test_baseline_model(X_train, y_train, X_test, y_test)
    
    # 2. 智能特征选择
    feature_selection_auc = test_feature_selection_improvement(X_train, y_train, X_test, y_test)
    if feature_selection_auc:
        results['feature_selection'] = feature_selection_auc
    
    # 3. 超参数优化
    results['hyperparameter_opt'] = test_hyperparameter_optimization(X_train, y_train, X_test, y_test)
    
    # 4. 模型集成
    ensemble_auc = test_ensemble_improvement(X_train, y_train, X_test, y_test)
    if ensemble_auc:
        results['ensemble'] = ensemble_auc
    
    # 输出结果总结
    logger.info("=" * 60)
    logger.info("AUC提升效果总结:")
    logger.info("=" * 60)
    
    baseline_auc = results['baseline']
    
    for method, auc in results.items():
        if method != 'baseline':
            improvement = auc - baseline_auc
            improvement_pct = (improvement / baseline_auc) * 100
            logger.info(f"{method:20s}: {auc:.4f} (+{improvement:+.4f}, +{improvement_pct:+.1f}%)")
    
    # 计算总体最佳提升
    best_auc = max(results.values())
    total_improvement = best_auc - baseline_auc
    total_improvement_pct = (total_improvement / baseline_auc) * 100
    
    logger.info("-" * 60)
    logger.info(f"{'基线AUC':20s}: {baseline_auc:.4f}")
    logger.info(f"{'最佳AUC':20s}: {best_auc:.4f}")
    logger.info(f"{'总体提升':20s}: +{total_improvement:.4f} (+{total_improvement_pct:.1f}%)")
    
    return results


if __name__ == "__main__":
    results = run_auc_improvement_tests()
