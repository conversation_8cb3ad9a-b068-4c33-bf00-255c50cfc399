
from datetime import datetime
import calendar


def get_date(base_date: str, months_to_add: int) -> str:
    base_date = datetime.strptime(base_date, "%Y%m%d")
    year = base_date.year
    month = base_date.month + months_to_add
    
    year += (month - 1) // 12
    month = (month - 1) % 12 + 1
    
    last_day = calendar.monthrange(year, month)[1]
    
    return datetime(year, month, last_day).strftime("%Y%m%d")
