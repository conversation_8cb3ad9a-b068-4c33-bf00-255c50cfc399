#!/usr/bin/env python
# coding: utf-8

"""
优化后的机器学习训练程序
主要优化：
1. 修复严重bug（文件路径、GridSearchCV使用等）
2. 性能优化（数据类型、内存使用、并行处理）
3. 代码结构优化（模块化、配置管理、异常处理）
4. 功能增强（交叉验证、模型评估、日志系统）
"""

import pandas as pd
import numpy as np
import sys
import json
import logging
import joblib
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass, field

from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.metrics import (
    roc_auc_score, f1_score, precision_score, recall_score,
    confusion_matrix, classification_report
)
from sklearn.ensemble import VotingClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression
try:
    from imblearn.over_sampling import SMOTE
    from imblearn.combine import SMOTETomek
    IMBLEARN_AVAILABLE = True
except ImportError:
    IMBLEARN_AVAILABLE = False

import xgboost as xgb
import lightgbm as lgb
from lightgbm import log_evaluation, early_stopping

try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

try:
    from skopt import BayesSearchCV
    from skopt.space import Real, Integer
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False

# 导入增强模型训练器
try:
    from enhanced_model_trainer import EnhancedModelTrainer
    ENHANCED_TRAINER_AVAILABLE = True
except ImportError:
    ENHANCED_TRAINER_AVAILABLE = False

# 配置pandas显示选项
pd.set_option('display.max_columns', 100)
pd.set_option('display.width', 1000)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 检查可选依赖
if not IMBLEARN_AVAILABLE:
    logger.warning("imblearn not available, skipping SMOTE")
if not CATBOOST_AVAILABLE:
    logger.warning("catboost not available")
if not SKOPT_AVAILABLE:
    logger.warning("scikit-optimize not available, using GridSearchCV")


@dataclass
class ModelConfig:
    """模型配置类，管理所有参数和路径"""
    
    # 路径配置
    base_path: str = '/home/<USER>/work'
    data_path: str = '/home/<USER>/work/fqt/csv'
    model_path: str = '/home/<USER>/work/userdata'
    
    # 模型参数
    test_size: float = 0.2
    random_state: int = 42
    cv_folds: int = 5
    early_stopping_rounds: int = 200
    
    # LightGBM默认参数
    lgb_params: Dict[str, Any] = field(default_factory=lambda: {
        'objective': 'binary',
        'boosting_type': 'gbdt',
        'min_split_gain': 0.0,
        'eval_metric': 'auc',
        'min_child_weight': 1,
        'verbose': -1,
        'random_state': 42
    })
    
    # 超参数搜索空间
    param_grid: Dict[str, List] = field(default_factory=lambda: {
        'num_leaves': [254],
        'learning_rate': [0.005],
        'n_estimators': [5000],
        'scale_pos_weight': [1],
        'feature_fraction': [0.75],
        'bagging_fraction': [0.75],
        'max_bin': [128]
    })
    
    def __post_init__(self):
        """确保路径存在"""
        for path in [self.data_path, self.model_path]:
            Path(path).mkdir(parents=True, exist_ok=True)


def get_date(base_date: str, months_to_add: int) -> str:
    """
    获取指定月份后的日期
    
    Args:
        base_date: 基准日期 (YYYYMMDD)
        months_to_add: 要添加的月数
        
    Returns:
        计算后的日期字符串
    """
    try:
        base_date_obj = datetime.strptime(base_date, "%Y%m%d")
        year = base_date_obj.year
        month = base_date_obj.month + months_to_add
        
        year += (month - 1) // 12
        month = (month - 1) % 12 + 1
        
        import calendar
        last_day = calendar.monthrange(year, month)[1]
        
        return datetime(year, month, last_day).strftime("%Y%m%d")
    except Exception as e:
        logger.error(f"日期计算错误: {e}")
        raise


def is_jupyter() -> bool:
    """检查是否在Jupyter环境中运行"""
    try:
        ipy_str = str(type(get_ipython()))
        if 'zmqshell' in ipy_str.lower():
            return True
        if 'terminal' in ipy_str.lower():
            return False
    except:
        pass
    
    for module in ['IPython', 'ipykernel', 'jupyter']:
        if module in sys.modules:
            return True
    
    return False


def get_drop_columns() -> List[str]:
    """
    获取需要删除的特征列表
    
    Returns:
        需要删除的列名列表
    """
    return [
        'bld_fnd_cst_ind_mean', 'preex_btch_cst_ind_mean', 'scr_fnds_mgt_cst_ind_mean',
        'long_py_sign_ind_mean', 'crnmo_smsbnk_fee_ind_mean', 'prvdfnd_lcrd_cst_ind_mean',
        'acpsmtl_sign_ind_mean', 'Rt12_Prd_Cnsmp_Int_MoNum', 'Instm_Bill_Bal',
        'chl_sttn_cd_mean', 'Repy_Dnum', 'Repy_Dnum_L4', 'mpbcstcmumblphmodlnum',
        'Repy_Dnum_L2', 'RT3Mo_Car_Svc_Cgy_Cnsmp_Amt', 'Instm_Grd_Dnum',
        'Repy_Dnum_L1', 'Instm_Grd_Dnum_L1', 'SpInstm_Bill_Bal',
        'stm_evl_cst_grd_cd_mean', 'Rvl_LnBal', 'Com_Instm_Bill_Bal',
        'Instm_Grd_Dnum_L2', 'R6Prd_Avg_Cash_Use_Rate', 'RT3Mo_Car_Svc_Cgy_CsmDnum',
        'mar_sttn_cd_mean', 'Bill_Instm_Bill_Bal', 'STMT_RTL_INTR_L6',
        'CrnMo_Car_Svc_Cgy_Cnsmp_Amt', 'R3Prd_Avg_Cash_Use_Rate', 'Instm_Fst_Amt_L6',
        'Rt6_Prd_DealW_Bill_Instm_Amt', 'RT3Mo_LgHmAppls_Cgy_Cnsmp_Amt',
        'STMT_CASH_INTR_L6', 'CrnMo_Car_Svc_Cgy_CsmDnum', 'mpbcstcrnmousmblphnum',
        'RT3Mo_HcBty_Cgy_Cnsmp_Amt', 'RT3Mo_TaxPymt_Cgy_Cnsmp_Amt',
        'STMT_CASH_INTR_L4', 'Rt6_Prd_Encshmt_Dnum', 'Rt3_Prd_Encshmt_Amt',
        'Sum_Draw_Amt_L5', 'Rt3_Prd_DealW_Bill_Instm_Amt', 'STMT_CASH_INTR_L2',
        'CrnMo_LgHmAppls_Cgy_Cnsmp_Amt', 'RT3Mo_Entmnt_Cgy_Cnsmp_Amt',
        'Rt6_Prd_Rvl_Cnt', 'Instm_Fst_Amt_L1', 'STMT_CASH_INTR_L1',
        'Instm_Fst_Amt', 'RT3Mo_Whlsl_Cgy_Cnsmp_Amt', 'RT3Mo_LgHmAppls_Cgy_CsmDnum',
        'CrnMo_Entmnt_Cgy_Cnsmp_Amt', 'Rt6_Prd_Encshmt_Int_MoNum',
        'CrnMo_Whlsl_Cgy_Cnsmp_Amt', 'RT3Mo_Tvl_Cgy_Cnsmp_Amt',
        'CrnMo_HcBty_Cgy_Cnsmp_Amt', 'CrnMo_TaxPymt_Cgy_Cnsmp_Amt',
        'Rt6_Prd_Cnsmp_Int_MoNum', 'RT3Mo_HcBty_Cgy_CsmDnum', 'STMT_RTL_INTR_L5',
        'RT3Mo_TaxPymt_Cgy_CsmDnum', 'CrnMo_Entmnt_Cgy_CsmDnum',
        'RT3Mo_Whlsl_Cgy_CsmDnum', 'RT3Mo_Entmnt_Cgy_CsmDnum', 'STMT_RTL_INTR_L2',
        'RT3Mo_Car_SCgy_Cnsmp_Amt', 'rt12mo_lpay_cst_ind_mean', 'mall_mbsh_ind_mean',
        'entp_adv_mgtppl_ind_mean', 'estb_2_cgy_acc_cst_ind_mean', 'mfs_sign_ind_mean',
        'crcrd_bndg_wechat_ind_mean', 'sms_bnk_sign_ind_mean', 'ccb_chmtpd_cst_ind_mean',
        'socins_lcrd_cst_ind_mean', 'etc_sign_ind_mean', 'ebnk_sign_st_ind_mean',
        'setlexp_crd_cst_ind_mean', 'idv_othr_lncst_ind_mean', 'fsln_cst_ind_mean',
        'gldcrd_cst_ind_mean', 'car_crd_cst_ind_mean', 'mpb_sign_st_ind_mean',
        'suying_cst_ind_mean', 'glblpy_crd_cst_ind_mean', 'tbnk_sign_ind_mean',
        'crcrd_rcsixmo_txn_ind_mean', 'rt12mo_fncpyrl_cst_ind_mean',
        'pln_fnc_efct_ind_mean', 'ins_cst_ind_mean', 'ccrd_avy_cst_ind_mean',
        'ebnkg_sign_ind_mean', 'accgld_bidirect_sign_ind_mean', 'rt12mocvnlvng_ind_mean',
        'sign_qikpay_ind_mean', 'crcrd_instm_cst_ind_mean', 'ccy_mkt_fnd_cst_ind_mean',
        'pos_mrch_sign_ind_mean', 'gldbutler_cst_ind_mean', 'ntw_bnk_sign_ind_mean',
        'stdnt_cst_ind_mean', 'inclsvln_cst_ind_mean', 'accgld_cst_ind_mean',
        'micro_gld_cst_ind_mean', 'freqrplcmblph_cst_ind_mean', 'frncy_dep_cst_ind_mean',
        'hgst_setl_acc_clcd_ind_mean', 'obnk_prvt_bnk_crd_cst_ind_mean', 'Rcvb_Bal',
        'corp_name_ind_mean', 'Com_Instm_Bal', 'eddgr_cd_mean',
        'rcv_mail_adr_tpcd_ind_mean', 'R12Prd_Avg_Cash_Use_Rate', 'cstmgr_id_ind_mean',
        'Sum_Consume_Cnt_L2', 'CrnPrd_CsmDnum', 'rsdnc_sttn_cd_mean',
        'wrk_unit_char_cd_mean', 'instm_cat_rate', 'mpbcstr12mumphmodlnum',
        'mpbcstrt12musmblphnum', 'fam_ppn_num_ind_mean', 'cust_type_ind_mean',
        'Sum_Consume_Cnt', 'crd_no_ind_mean', 'ptnl_vip_ind_mean', 'pyrl_cst_ind_mean',
        'ccrd_actvt_cst_ind_mean', 'empchnl_bsop_ind_mean', 'estb_3_cgy_acc_cst_ind_mean',
        'cst_chnl_bsop_ind_mean', 'spclvip_ind_mean', 'chmtpd_cst_ind_mean',
        'tra_lcrd_cst_ind_mean', 'crcrd_sign_auto_repy_ind_mean', 'fnd_cst_ind_mean',
        'rt12mo_pyrl_cst_ind_mean', 'bond_fnd_cst_ind_mean', 'dbcrd_bndg_wechat_ind_mean',
        'etc_cst_ind_mean', 'entp_act_ctrl_psn_ind_mean', 'epa_cst_ind_mean',
        'yuelf_cst_ind_mean', 'enlgps_ind_mean', 'crt_insid_ind_mean',
        'Cust_Org_Num_ind_mean', 'idy_tpcd_mean', 'stmt_adr_ziped_ind_mean',
        'cst_label_ind_mean', 'pref_msnd_mtdcd_ind_mean', 'RT6Mo_Com_Instm_Amt',
        'RT3Mo_Instm_Amt', 'RT6Mo_Bill_Instm_Amt', 'PrvtCtCT910DOBCCRBNum',
        'RT6Mo_Cash_Instm_Amt', 'RT3Mo_Com_Instm_Amt', 'RT6Mo_Instm_Dnum',
        'CrnMo_Instm_Amt', 'RT3Mo_Bill_Instm_Amt', 'PrvtCstCT910DWLDDDnum',
        'PrvtCstCT910DWLDRDnum', 'PrvtCstCT910DCFCRDnum', 'PrvtCstCT910DCFCDDnum',
        'PrvtCtCT910DJDBMRDnum', 'PrvtCstCT910DWFCDDnum', 'RT6Mo_SpInstm_Amt',
        'CCrdCstEnmtHPrblSclCd', 'R3m_Avg_Repy_Rate', 'PrvtCtCT910DOBCCRCNum',
        'PrvtCstMACCBCCReyDnum', 'CCrdCstLwtRHPrblSclCd', 'PrvtCstMAOBCCRepyDnum',
        'CCCHlCcCvPlScCd', 'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd',
        'CCrdCstLRHPrblScorVal', 'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc',
        'CCCHlCcCvPlScCd', 'R3m_Avg_Repy_Rate', 'CrnMo_Instm_Amt',
        'RT3Mo_Instm_Amt', 'RT3Mo_Com_Instm_Amt', 'RT3Mo_Bill_Instm_Amt',
        'RT6Mo_Instm_Amt', 'RT6Mo_Instm_Dnum', 'RT6Mo_SpInstm_Amt',
        'RT6Mo_Com_Instm_Amt', 'RT6Mo_Bill_Instm_Amt', 'RT6Mo_Cash_Instm_Amt',
        'PrvtCstCT910DWLDDDnum', 'PrvtCstCT910DWLDRDnum', 'PrvtCstCT910DMCRyDnum',
        'PrvtCtCT910DJDBMRDnum', 'PrvtCstCT910DCFCDDnum', 'PrvtCstCT910DCFCRDnum',
        'PrvtCstCT910DWFCDDnum', 'PrvtCstMAOBCCRepyDnum', 'PrvtCstMACCBCCReyDnum',
        'PrvtCstMACCBCCRCrdNum', 'PrvtCtCT910DOBCCRDnum', 'PrvtCtCT910DOBCCRCNum',
        'PrvtCtCT910DOBCCRBNum', 'PrvtCCT910DCCBCCRDnum', 'PrvtCCT910DCCBCCRCNum',
        'PrvtCstCT910DLReyDnum'
    ]


def optimize_data_types(df: pd.DataFrame) -> pd.DataFrame:
    """
    优化数据类型以减少内存使用

    Args:
        df: 输入数据框

    Returns:
        优化后的数据框
    """
    logger.info("开始优化数据类型...")

    original_memory = df.memory_usage(deep=True).sum() / 1024**2

    # 处理字符串类型的分类特征 - 转换为数值编码
    for col in df.select_dtypes(include=['object']).columns:
        if col not in ['cst_keywrd']:  # 保留客户ID为字符串
            # 使用LabelEncoder转换字符串为数值
            from sklearn.preprocessing import LabelEncoder
            le = LabelEncoder()
            df[col] = le.fit_transform(df[col].astype(str))

    # 优化数值列
    for col in df.select_dtypes(include=['int64']).columns:
        if df[col].min() >= 0:
            if df[col].max() < 255:
                df[col] = df[col].astype('uint8')
            elif df[col].max() < 65535:
                df[col] = df[col].astype('uint16')
            elif df[col].max() < 4294967295:
                df[col] = df[col].astype('uint32')
        else:
            if df[col].min() > -128 and df[col].max() < 127:
                df[col] = df[col].astype('int8')
            elif df[col].min() > -32768 and df[col].max() < 32767:
                df[col] = df[col].astype('int16')
            elif df[col].min() > -2147483648 and df[col].max() < 2147483647:
                df[col] = df[col].astype('int32')

    # 优化浮点数
    for col in df.select_dtypes(include=['float64']).columns:
        df[col] = pd.to_numeric(df[col], downcast='float')

    optimized_memory = df.memory_usage(deep=True).sum() / 1024**2
    memory_reduction = (original_memory - optimized_memory) / original_memory * 100

    logger.info(f"内存使用优化完成: {original_memory:.2f}MB -> {optimized_memory:.2f}MB "
               f"(减少 {memory_reduction:.1f}%)")

    return df


def drop_columns(df: pd.DataFrame) -> pd.DataFrame:
    """
    删除不需要的特征列
    
    Args:
        df: 输入数据框
        
    Returns:
        删除特征后的数据框
    """
    drop_cols = get_drop_columns()
    
    # 记录删除前后的列数
    original_cols = len(df.columns)
    df.drop(drop_cols, axis=1, inplace=True, errors='ignore')
    remaining_cols = len(df.columns)
    
    logger.info(f"特征删除完成: {original_cols} -> {remaining_cols} "
               f"(删除 {original_cols - remaining_cols} 个特征)")
    
    return df


class OptimizedModelTrainer:
    """
    优化后的模型训练器

    主要优化：
    1. 修复原有bug
    2. 分离训练和预测逻辑
    3. 添加完整的异常处理
    4. 改进模型评估和保存
    """

    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
        self.feature_names = None
        self.logger = self._setup_logger()

    def _setup_logger(self) -> logging.Logger:
        """设置专用日志记录器"""
        logger = logging.getLogger(f'ModelTrainer_{id(self)}')
        logger.setLevel(logging.INFO)

        # 避免重复添加处理器
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def load_data(self, train_file: str, test_file: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        加载训练和测试数据

        Args:
            train_file: 训练数据文件名
            test_file: 测试数据文件名

        Returns:
            训练和测试数据框
        """
        try:
            train_path = Path(self.config.data_path) / train_file
            test_path = Path(self.config.data_path) / test_file

            if not train_path.exists():
                raise FileNotFoundError(f"训练数据文件不存在: {train_path}")
            if not test_path.exists():
                raise FileNotFoundError(f"测试数据文件不存在: {test_path}")

            self.logger.info(f"加载训练数据: {train_path}")
            df_train = pd.read_csv(train_path, dtype={'cst_keywrd': 'object'})

            self.logger.info(f"加载测试数据: {test_path}")
            df_test = pd.read_csv(test_path, dtype={'cst_keywrd': 'object'})

            self.logger.info(f"数据加载完成 - 训练: {df_train.shape}, 测试: {df_test.shape}")

            return df_train, df_test

        except Exception as e:
            self.logger.error(f"数据加载失败: {e}")
            raise

    def preprocess_data(self, df_train: pd.DataFrame, df_test: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        数据预处理

        Args:
            df_train: 训练数据
            df_test: 测试数据

        Returns:
            预处理后的训练和测试数据
        """
        try:
            self.logger.info("开始数据预处理...")

            # 删除不需要的特征
            df_train = drop_columns(df_train)
            df_test = drop_columns(df_test)

            # 优化数据类型
            df_train = optimize_data_types(df_train)
            df_test = optimize_data_types(df_test)

            # 检查数据质量
            self._validate_data(df_train, df_test)

            self.logger.info("数据预处理完成")
            return df_train, df_test

        except Exception as e:
            self.logger.error(f"数据预处理失败: {e}")
            raise

    def _validate_data(self, df_train: pd.DataFrame, df_test: pd.DataFrame) -> None:
        """验证数据质量"""
        # 检查必要列是否存在
        required_cols = ['cst_keywrd', 'target']
        for col in required_cols:
            if col not in df_train.columns:
                raise ValueError(f"训练数据缺少必要列: {col}")

        if 'cst_keywrd' not in df_test.columns:
            raise ValueError("测试数据缺少客户ID列: cst_keywrd")

        # 检查目标变量分布
        target_dist = df_train['target'].value_counts()
        self.logger.info(f"目标变量分布: {target_dist.to_dict()}")

        if len(target_dist) != 2:
            self.logger.warning("目标变量不是二分类问题")

        # 检查特征列一致性
        train_features = set(df_train.columns) - {'cst_keywrd', 'target'}
        test_features = set(df_test.columns) - {'cst_keywrd', 'target'}

        missing_in_test = train_features - test_features
        extra_in_test = test_features - train_features

        if missing_in_test:
            self.logger.warning(f"测试数据缺少特征: {missing_in_test}")
        if extra_in_test:
            self.logger.warning(f"测试数据多余特征: {extra_in_test}")

    def train_model(self, df_train: pd.DataFrame, cst_id: str = 'cst_keywrd',
                   target: str = 'target') -> Dict[str, Any]:
        """
        训练模型

        Args:
            df_train: 训练数据
            cst_id: 客户ID列名
            target: 目标列名

        Returns:
            训练结果字典
        """
        try:
            self.logger.info("开始模型训练...")

            # 准备训练数据
            X = df_train.drop([cst_id, target], axis=1)
            y = df_train[target]
            self.feature_names = X.columns.tolist()

            # 数据分割
            X_train, X_val, y_train, y_val = train_test_split(
                X, y,
                test_size=self.config.test_size,
                stratify=y,
                random_state=self.config.random_state
            )

            self.logger.info(f"数据分割完成 - 训练: {X_train.shape}, 验证: {X_val.shape}")

            # 超参数调优
            best_params = self._hyperparameter_tuning(X_train, y_train)

            # 使用最佳参数训练最终模型
            final_params = {**self.config.lgb_params, **best_params}

            self.model = lgb.LGBMClassifier(**final_params)

            # 训练模型
            eval_set = [(X_val, y_val)]
            self.model.fit(
                X_train, y_train,
                eval_set=eval_set,
                eval_metric='auc',
                callbacks=[
                    early_stopping(self.config.early_stopping_rounds, verbose=False),
                    log_evaluation(period=100, show_stdv=False)
                ]
            )

            # 模型评估
            train_results = self._evaluate_model(X_train, y_train, X_val, y_val)

            self.logger.info("模型训练完成")
            return train_results

        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            raise

    def _hyperparameter_tuning(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict[str, Any]:
        """
        超参数调优

        Args:
            X_train: 训练特征
            y_train: 训练标签

        Returns:
            最佳参数字典
        """
        try:
            self.logger.info("开始超参数调优...")

            # 创建基础模型
            base_model = lgb.LGBMClassifier(**self.config.lgb_params)

            # 网格搜索 - 修复原有bug：设置refit=True，移除不支持的参数
            grid_search = GridSearchCV(
                estimator=base_model,
                param_grid=self.config.param_grid,
                scoring='roc_auc',
                cv=self.config.cv_folds,
                n_jobs=-1,
                refit=True,  # 修复bug：设置为True
                verbose=1
            )

            # 执行网格搜索 - 修复bug：移除eval_set参数
            grid_search.fit(X_train, y_train)

            best_params = grid_search.best_params_
            best_score = grid_search.best_score_

            self.logger.info(f"超参数调优完成 - 最佳CV AUC: {best_score:.4f}")
            self.logger.info(f"最佳参数: {best_params}")

            return best_params

        except Exception as e:
            self.logger.error(f"超参数调优失败: {e}")
            raise

    def _evaluate_model(self, X_train: pd.DataFrame, y_train: pd.Series,
                       X_val: pd.DataFrame, y_val: pd.Series) -> Dict[str, Any]:
        """
        评估模型性能

        Args:
            X_train: 训练特征
            y_train: 训练标签
            X_val: 验证特征
            y_val: 验证标签

        Returns:
            评估结果字典
        """
        try:
            # 预测
            y_train_pred = self.model.predict(X_train)
            y_train_proba = self.model.predict_proba(X_train)[:, 1]
            y_val_pred = self.model.predict(X_val)
            y_val_proba = self.model.predict_proba(X_val)[:, 1]

            # 计算指标
            results = {
                'train_auc': roc_auc_score(y_train, y_train_proba),
                'val_auc': roc_auc_score(y_val, y_val_proba),
                'train_f1': f1_score(y_train, y_train_pred),
                'val_f1': f1_score(y_val, y_val_pred),
                'train_precision': precision_score(y_train, y_train_pred),
                'val_precision': precision_score(y_val, y_val_pred),
                'train_recall': recall_score(y_train, y_train_pred),
                'val_recall': recall_score(y_val, y_val_pred)
            }

            # 记录评估结果
            self.logger.info("模型评估结果:")
            for metric, value in results.items():
                self.logger.info(f"  {metric}: {value:.4f}")

            # 混淆矩阵
            cm_val = confusion_matrix(y_val, y_val_pred)
            self.logger.info(f"验证集混淆矩阵:\n{cm_val}")

            return results

        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            raise

    def save_model(self, feat_ver: str, model_ver: str) -> None:
        """
        保存模型和参数

        Args:
            feat_ver: 特征版本
            model_ver: 模型版本
        """
        try:
            if self.model is None:
                raise ValueError("模型尚未训练，无法保存")

            model_path = Path(self.config.model_path)

            # 保存模型文件 - 修复bug：统一文件扩展名
            model_file = model_path / f'{feat_ver}_{model_ver}.lgb'
            self.model.booster_.save_model(str(model_file))

            # 保存模型参数 - 修复bug：统一使用.json扩展名
            params_file = model_path / f'{feat_ver}_{model_ver}.json'
            model_params = self.model.get_params()
            with open(params_file, 'w') as f:
                json.dump(model_params, f, indent=2)

            # 保存特征重要性
            if self.feature_names:
                importance_file = model_path / f'import{feat_ver}_{model_ver}_lgb.csv'
                self._save_feature_importance(importance_file)

            self.logger.info(f"模型保存完成:")
            self.logger.info(f"  模型文件: {model_file}")
            self.logger.info(f"  参数文件: {params_file}")
            self.logger.info(f"  重要性文件: {importance_file}")

        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            raise

    def _save_feature_importance(self, importance_file: Path) -> None:
        """保存特征重要性"""
        try:
            feature_importances = self.model.booster_.feature_importance(importance_type='gain')

            importance_df = pd.DataFrame({
                'feature': self.feature_names,
                'importance': feature_importances
            }).sort_values(by='importance', ascending=False)

            importance_df.fillna(value=0, inplace=True)
            importance_df.to_csv(importance_file, index=False)

            # 记录前10个重要特征
            top_features = importance_df.head(10)
            self.logger.info("前10个重要特征:")
            for _, row in top_features.iterrows():
                self.logger.info(f"  {row['feature']}: {row['importance']:.2f}")

        except Exception as e:
            self.logger.error(f"保存特征重要性失败: {e}")
            raise

    def load_model(self, feat_ver: str, model_ver: str) -> None:
        """
        加载已保存的模型

        Args:
            feat_ver: 特征版本
            model_ver: 模型版本
        """
        try:
            model_path = Path(self.config.model_path)

            # 加载参数文件 - 修复bug：统一文件扩展名
            params_file = model_path / f'{feat_ver}_{model_ver}.json'
            if not params_file.exists():
                raise FileNotFoundError(f"参数文件不存在: {params_file}")

            with open(params_file, 'r') as f:
                loaded_params = json.load(f)

            # 加载模型文件
            model_file = model_path / f'{feat_ver}_{model_ver}.lgb'
            if not model_file.exists():
                raise FileNotFoundError(f"模型文件不存在: {model_file}")

            loaded_booster = lgb.Booster(model_file=str(model_file))

            # 重建模型
            self.model = lgb.LGBMClassifier(**loaded_params)
            self.model._Booster = loaded_booster

            self.logger.info(f"模型加载完成: {model_file}")

        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise

    def predict(self, df_test: pd.DataFrame, cst_id: str = 'cst_keywrd') -> np.ndarray:
        """
        使用训练好的模型进行预测

        Args:
            df_test: 测试数据
            cst_id: 客户ID列名

        Returns:
            预测概率数组
        """
        try:
            if self.model is None:
                raise ValueError("模型尚未训练或加载，无法进行预测")

            # 准备测试数据
            X_test = df_test.drop([cst_id], axis=1, errors='ignore')
            if 'target' in X_test.columns:
                X_test = X_test.drop(['target'], axis=1)

            # 预测
            y_pred_proba = self.model.predict_proba(X_test)[:, 1]

            self.logger.info(f"预测完成，样本数: {len(y_pred_proba)}")
            self.logger.info(f"预测概率范围: [{y_pred_proba.min():.4f}, {y_pred_proba.max():.4f}]")

            return y_pred_proba

        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise


def run_optimized_training(base_date: str, is_predict_only: bool = False) -> None:
    """
    运行优化后的模型训练流程

    Args:
        base_date: 基准日期 (YYYYMMDD)
        is_predict_only: 是否仅进行预测
    """
    start_time = time.time()
    logger.info(f"开始运行优化后的模型训练流程 - 基准日期: {base_date}")

    try:
        # 初始化配置
        config = ModelConfig()

        # 计算日期
        train_date = base_date
        test_date = get_date(train_date, 1)

        # 构建文件名
        train_file = f'train202503f_{train_date}.csv'
        test_file = f'test202503f_{test_date}.csv'

        # 设置版本信息
        feat_ver = '202509'
        model_ver = f'4.9_{train_date}'

        # 选择训练器
        if ENHANCED_TRAINER_AVAILABLE:
            logger.info("使用增强模型训练器...")
            trainer = EnhancedModelTrainer(config)

            # 加载数据
            df_train = pd.read_csv(f"{config.data_path}/{train_file}", dtype={'cst_keywrd': 'object'})
            df_test = pd.read_csv(f"{config.data_path}/{test_file}", dtype={'cst_keywrd': 'object'})
        else:
            # 使用原始训练器
            logger.info("使用原始模型训练器...")
            trainer = OptimizedModelTrainer(config)

            # 加载数据
            df_train, df_test = trainer.load_data(train_file, test_file)

            # 数据预处理
            df_train, df_test = trainer.preprocess_data(df_train, df_test)

        if is_predict_only:
            # 仅预测模式
            logger.info("运行预测模式...")
            trainer.load_model(feat_ver, model_ver)
            y_pred_proba = trainer.predict(df_test)
        else:
            # 训练模式
            logger.info("运行训练模式...")

            # 训练模型
            train_results = trainer.train_model(df_train)

            # 保存模型
            trainer.save_model(feat_ver, model_ver)

            # 预测
            y_pred_proba = trainer.predict(df_test)

        # 保存预测结果
        save_predictions(df_test, y_pred_proba, train_date, test_date, config)

        # 计算运行时间
        end_time = time.time()
        runtime = end_time - start_time

        logger.info(f"程序运行完成，总耗时: {runtime:.2f} 秒")

    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        raise


def save_predictions(df_test: pd.DataFrame, y_pred_proba: np.ndarray,
                    train_date: str, test_date: str, config: ModelConfig) -> None:
    """
    保存预测结果

    Args:
        df_test: 测试数据
        y_pred_proba: 预测概率
        train_date: 训练日期
        test_date: 测试日期
        config: 配置对象
    """
    try:
        # 创建预测结果DataFrame
        pred_df = pd.DataFrame({
            'cst_keywrd': df_test['cst_keywrd'],
            'y_lgb2': y_pred_proba
        })

        # 构建输出文件名
        output_file = f'subf202503m4.8_{train_date}_{test_date}.csv'
        output_path = Path(config.data_path) / output_file

        # 保存结果
        pred_df.to_csv(output_path, index=False)

        logger.info(f"预测结果已保存: {output_path}")
        logger.info(f"预测样本数: {len(pred_df)}")
        logger.info(f"预测概率统计:")
        logger.info(f"  均值: {y_pred_proba.mean():.4f}")
        logger.info(f"  标准差: {y_pred_proba.std():.4f}")
        logger.info(f"  最小值: {y_pred_proba.min():.4f}")
        logger.info(f"  最大值: {y_pred_proba.max():.4f}")

    except Exception as e:
        logger.error(f"保存预测结果失败: {e}")
        raise


def main():
    """主函数"""
    try:
        if is_jupyter():
            # Jupyter环境
            base_date = '20250228'  # 可以修改为需要的日期
            logger.info(f"Jupyter环境运行，使用默认日期: {base_date}")
        else:
            # 命令行环境
            if len(sys.argv) != 2:
                print(f"Usage: {sys.argv[0]} YYYYMMDD")
                sys.exit(1)
            base_date = sys.argv[1]

        # 验证日期格式
        try:
            datetime.strptime(base_date, "%Y%m%d")
        except ValueError:
            logger.error(f"无效的日期格式: {base_date}，应为YYYYMMDD")
            sys.exit(1)

        # 运行训练流程
        run_optimized_training(base_date, is_predict_only=False)

    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
