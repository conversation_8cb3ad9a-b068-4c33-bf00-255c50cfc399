import time
from DrissionPage import ChromiumPage



def wait_for_modal(page, modal_locator, timeout=2, check_interval=0.5):

    end_time = time.time() + timeout
    while time.time() < end_time:
        try:
            modal = page.ele(modal_locator, timeout=0)
            if modal.is_displayed() and modal.is_enabled():
                return modal
        except :
            pass
        time.sleep(check_interval)




page = ChromiumPage()


page.get("http://ai.jh")  
page.ele('xpath://a[contains(text(), "众研工作台")]').click()

try:

    login_name = page.ele('xpath://input[@placeholder="请输入UASS用户名"]')

    login_name.input("houwei.jx") 
    print(login_name)

    if login_name:
        page.ele('xpath://input[@placeholder="请输入UASS密码"]').input("Jx86848216") 
        page.ele('xpath://*/form/div[4]/div/button').click()  

except Exception as e:
    pass



page.ele("xpath://span[contains(text(), '数据准备')]").click()
         
page.ele("xpath://li[contains(text(), '数据接入')]").click()         

page.eles("立即运行")[0].click(by_js=None)

time.sleep(2)


dialog = page.ele('@class=el-button el-button--default el-button--small el-button--primary ')
print(dialog)
dialog.click()




# ele.click(by_js=True)



# right_links = [
#     "xpath://div[@id='content']//a[contains(text(), '链接1')]", 
#     "xpath://div[@id='content']//a[contains(text(), '链接2')]",
#     "xpath://div[@id='content']//a[contains(text(), '链接3')]"
# ]

# try:
#     while True:
#         for link_selector in right_links:
#             # 等待链接可点击并点击
#             page.wait.ele_clickable(link_selector, timeout=10).click()
#             time.sleep(1)  # 等待页面加载
        
#         time.sleep(180)  # 等待3分钟

# except KeyboardInterrupt:
#     print("脚本已停止")

# finally:
#     page.quit()

