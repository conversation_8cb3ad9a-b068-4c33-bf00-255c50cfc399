#!/usr/bin/env python
# coding: utf-8

# In[11]:


# 增加lgb_train_predict，评估特征效果代码


# In[12]:


#### coding=utf-8


import pandas as pd
import numpy as np

from sklearn.model_selection import train_test_split, GridSearchCV
import xgboost as xgb
import lightgbm as lgb
#import catboost as ctb
from lightgbm import log_evaluation
from lightgbm import early_stopping
from sklearn.ensemble import RandomForestClassifier
from sklearn import metrics
from sklearn.ensemble import StackingClassifier
from sklearn.model_selection import StratifiedShuffleSplit
from sklearn.model_selection import KFold, StratifiedKFold,GroupKFold
from sklearn.metrics import f1_score
from sklearn.utils.estimator_checks import check_estimator
from sklearn.base import BaseEstimator
from sklearn.base import ClassifierMixin
from sklearn.metrics import confusion_matrix
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score
from sklearn.metrics import recall_score, make_scorer
import json
import sys
from datetime import datetime
import calendar

import logging
import joblib
import time


# In[13]:



def get_date(base_date, months_to_add):
    base_date = datetime.strptime(base_date, "%Y%m%d")
    year = base_date.year
    month = base_date.month + months_to_add
    
    year += (month - 1) // 12
    month = (month - 1) % 12 + 1
    
    last_day = calendar.monthrange(year, month)[1]
    
    return datetime(year, month, last_day).strftime("%Y%m%d")

def is_jupyter():
    try:
        ipy_str = str(type(get_ipython()))
        if 'zmqshell' in ipy_str.lower():
            return True
        if 'terminal' in ipy_str.lower():
            return False
    except:
        pass
    
    for module in ['IPython', 'ipykernel', 'jupyter']:
        if module in sys.modules:
            return True
    
    return False


# In[14]:


def drop_cols(df):
    drop_cols = [
        'bld_fnd_cst_ind_mean',
        'preex_btch_cst_ind_mean',
        'scr_fnds_mgt_cst_ind_mean',
        'long_py_sign_ind_mean',
#         'psnloan_cst_ind_mean',   #==
        'crnmo_smsbnk_fee_ind_mean',
        'prvdfnd_lcrd_cst_ind_mean',
        'acpsmtl_sign_ind_mean',
#         'idv_hsln_cst_ind_mean',  #==
#         'sp_level_ind_mean',
#         'ttl_tpcd_mean',
#         'cust_level_ind_mean',
#         'loan_cst_ind_mean',==
#         'yx_level_ind_mean',
#         'thismon_new_ind_mean',
#         'lastmon_new_ind_mean',        
        'Rt12_Prd_Cnsmp_Int_MoNum',
         'Instm_Bill_Bal',
         'chl_sttn_cd_mean',
         'Repy_Dnum',
         'Repy_Dnum_L4',
         'mpbcstcmumblphmodlnum',
         'Repy_Dnum_L2',
         'RT3Mo_Car_Svc_Cgy_Cnsmp_Amt',
         'Instm_Grd_Dnum',
         'Repy_Dnum_L1',
         'Instm_Grd_Dnum_L1',
         'SpInstm_Bill_Bal',
         'stm_evl_cst_grd_cd_mean',
         'Rvl_LnBal',
         'Com_Instm_Bill_Bal',
         'Instm_Grd_Dnum_L2',
         'R6Prd_Avg_Cash_Use_Rate',
         'RT3Mo_Car_Svc_Cgy_CsmDnum',
         'mar_sttn_cd_mean',
         'Bill_Instm_Bill_Bal',
         'STMT_RTL_INTR_L6',
         'CrnMo_Car_Svc_Cgy_Cnsmp_Amt',
         'R3Prd_Avg_Cash_Use_Rate',
         'Instm_Fst_Amt_L6',
         'Rt6_Prd_DealW_Bill_Instm_Amt',
         'RT3Mo_LgHmAppls_Cgy_Cnsmp_Amt',
         'STMT_CASH_INTR_L6',
         'CrnMo_Car_Svc_Cgy_CsmDnum',
#          'dgr_cd_mean',
         'mpbcstcrnmousmblphnum',
         'RT3Mo_HcBty_Cgy_Cnsmp_Amt',
#          'gnd_cd_mean',
         'RT3Mo_TaxPymt_Cgy_Cnsmp_Amt',
         'STMT_CASH_INTR_L4',
         'Rt6_Prd_Encshmt_Dnum',
         'Rt3_Prd_Encshmt_Amt',
         'Sum_Draw_Amt_L5',
         'Rt3_Prd_DealW_Bill_Instm_Amt',
         'STMT_CASH_INTR_L2',
         'CrnMo_LgHmAppls_Cgy_Cnsmp_Amt',
         'RT3Mo_Entmnt_Cgy_Cnsmp_Amt',
         'Rt6_Prd_Rvl_Cnt',
         'Instm_Fst_Amt_L1',
         'STMT_CASH_INTR_L1',
         'Instm_Fst_Amt',
         'RT3Mo_Whlsl_Cgy_Cnsmp_Amt',
         'RT3Mo_LgHmAppls_Cgy_CsmDnum',
         'CrnMo_Entmnt_Cgy_Cnsmp_Amt',
         'Rt6_Prd_Encshmt_Int_MoNum',
         'CrnMo_Whlsl_Cgy_Cnsmp_Amt',
         'RT3Mo_Tvl_Cgy_Cnsmp_Amt',
         'CrnMo_HcBty_Cgy_Cnsmp_Amt',
         'CrnMo_TaxPymt_Cgy_Cnsmp_Amt',
         'Rt6_Prd_Cnsmp_Int_MoNum',
         'RT3Mo_HcBty_Cgy_CsmDnum',
         'STMT_RTL_INTR_L5',
         'RT3Mo_TaxPymt_Cgy_CsmDnum',
         'CrnMo_Entmnt_Cgy_CsmDnum',
         'RT3Mo_Whlsl_Cgy_CsmDnum',
         'RT3Mo_Entmnt_Cgy_CsmDnum',
         'STMT_RTL_INTR_L2',
         'RT3Mo_Car_SCgy_Cnsmp_Amt',
         'rt12mo_lpay_cst_ind_mean',
         'mall_mbsh_ind_mean',
         'entp_adv_mgtppl_ind_mean',
         'estb_2_cgy_acc_cst_ind_mean',
         'mfs_sign_ind_mean',
         'crcrd_bndg_wechat_ind_mean',
         'sms_bnk_sign_ind_mean',
         'ccb_chmtpd_cst_ind_mean',
         'socins_lcrd_cst_ind_mean',
         'etc_sign_ind_mean',
         'ebnk_sign_st_ind_mean',
         'setlexp_crd_cst_ind_mean',
         'idv_othr_lncst_ind_mean',
         'fsln_cst_ind_mean',
         'gldcrd_cst_ind_mean',
         'car_crd_cst_ind_mean',
         'mpb_sign_st_ind_mean',
         'suying_cst_ind_mean',
         'glblpy_crd_cst_ind_mean',
         'tbnk_sign_ind_mean',
         'crcrd_rcsixmo_txn_ind_mean',
         'rt12mo_fncpyrl_cst_ind_mean',
         'pln_fnc_efct_ind_mean',
         'ins_cst_ind_mean',
         'ccrd_avy_cst_ind_mean',
         'ebnkg_sign_ind_mean',
         'accgld_bidirect_sign_ind_mean',
         'rt12mocvnlvng_ind_mean',
         'sign_qikpay_ind_mean',
         'crcrd_instm_cst_ind_mean',
         'ccy_mkt_fnd_cst_ind_mean',
         'pos_mrch_sign_ind_mean',
         'gldbutler_cst_ind_mean',
         'ntw_bnk_sign_ind_mean',
         'stdnt_cst_ind_mean',
         'inclsvln_cst_ind_mean',
         'accgld_cst_ind_mean',
         'micro_gld_cst_ind_mean',
         'freqrplcmblph_cst_ind_mean',
         'frncy_dep_cst_ind_mean',
#          'mpb_sign_ind_mean',
         'hgst_setl_acc_clcd_ind_mean',
         'obnk_prvt_bnk_crd_cst_ind_mean',
         'Rcvb_Bal',
         'corp_name_ind_mean',
         'Com_Instm_Bal',
         'eddgr_cd_mean',
         'rcv_mail_adr_tpcd_ind_mean',
         'R12Prd_Avg_Cash_Use_Rate',
         'cstmgr_id_ind_mean',
         'Sum_Consume_Cnt_L2',
         'CrnPrd_CsmDnum',
         'rsdnc_sttn_cd_mean',
         'wrk_unit_char_cd_mean',
         'instm_cat_rate',
         'mpbcstr12mumphmodlnum',
         'mpbcstrt12musmblphnum',
         'fam_ppn_num_ind_mean',
         'cust_type_ind_mean',
         'Sum_Consume_Cnt',
         'crd_no_ind_mean',
         'ptnl_vip_ind_mean',
         'pyrl_cst_ind_mean',
         'ccrd_actvt_cst_ind_mean',
         'empchnl_bsop_ind_mean',
         'estb_3_cgy_acc_cst_ind_mean',
         'cst_chnl_bsop_ind_mean',
         'spclvip_ind_mean',
         'chmtpd_cst_ind_mean',
         'tra_lcrd_cst_ind_mean',
         'crcrd_sign_auto_repy_ind_mean',
         'fnd_cst_ind_mean',
         'rt12mo_pyrl_cst_ind_mean',
         'bond_fnd_cst_ind_mean',
#          'instm_cat_num',
         'dbcrd_bndg_wechat_ind_mean',
         'etc_cst_ind_mean',
         'entp_act_ctrl_psn_ind_mean',
         'epa_cst_ind_mean',
         'yuelf_cst_ind_mean',
         'enlgps_ind_mean',
         'crt_insid_ind_mean',
#          'ocp_cd_mean',
         'Cust_Org_Num_ind_mean',
         'idy_tpcd_mean',
         'stmt_adr_ziped_ind_mean',
         'cst_label_ind_mean',
#          'bblng_insid_ind_mean',
#          'best_ctc_tel_ind_mean',
         'pref_msnd_mtdcd_ind_mean',
         'RT6Mo_Com_Instm_Amt',
         'RT3Mo_Instm_Amt',
         'RT6Mo_Bill_Instm_Amt',
         'PrvtCtCT910DOBCCRBNum',
         'RT6Mo_Cash_Instm_Amt',
         'RT3Mo_Com_Instm_Amt',
         'RT6Mo_Instm_Dnum',
         'CrnMo_Instm_Amt',
         'RT3Mo_Bill_Instm_Amt',
         'PrvtCstCT910DWLDDDnum',
         'PrvtCstCT910DWLDRDnum',
         'PrvtCstCT910DCFCRDnum',
         'PrvtCstCT910DCFCDDnum',
         'PrvtCtCT910DJDBMRDnum',
         'PrvtCstCT910DWFCDDnum',

         # missing_ration>0.5
         'RT6Mo_SpInstm_Amt',
         'CCrdCstEnmtHPrblSclCd',
         'R3m_Avg_Repy_Rate',
         'PrvtCtCT910DOBCCRCNum',
         'PrvtCstMACCBCCReyDnum',
         'CCrdCstLwtRHPrblSclCd',
         'PrvtCstMAOBCCRepyDnum',
         'CCCHlCcCvPlScCd',
         'CCrdCstEtHPrblScorVal',
         'CCrdCstEnmtHPrblSclCd',
         'CCrdCstLRHPrblScorVal',
         'CCrdCstLwtRHPrblSclCd',
         'CCCHlCcCvPlSc',
         'CCCHlCcCvPlScCd',
         'R3m_Avg_Repy_Rate',
         'CrnMo_Instm_Amt',
         'RT3Mo_Instm_Amt',
         'RT3Mo_Com_Instm_Amt',
         'RT3Mo_Bill_Instm_Amt',
         'RT6Mo_Instm_Amt',
         'RT6Mo_Instm_Dnum',
         'RT6Mo_SpInstm_Amt',
         'RT6Mo_Com_Instm_Amt',
         'RT6Mo_Bill_Instm_Amt',
         'RT6Mo_Cash_Instm_Amt',
         'PrvtCstCT910DWLDDDnum',
         'PrvtCstCT910DWLDRDnum',
         'PrvtCstCT910DMCRyDnum',
         'PrvtCtCT910DJDBMRDnum',
         'PrvtCstCT910DCFCDDnum',
         'PrvtCstCT910DCFCRDnum',
         'PrvtCstCT910DWFCDDnum',
         'PrvtCstMAOBCCRepyDnum',
         'PrvtCstMACCBCCReyDnum',
         'PrvtCstMACCBCCRCrdNum',
         'PrvtCtCT910DOBCCRDnum',
         'PrvtCtCT910DOBCCRCNum',
         'PrvtCtCT910DOBCCRBNum',
         'PrvtCCT910DCCBCCRDnum',
         'PrvtCCT910DCCBCCRCNum',
         'PrvtCstCT910DLReyDnum'
    ]
    
    df.drop(drop_cols, axis=1, inplace=True, errors='ignore')
    
    return df


# In[15]:


def setup_logger(log_file):
    logger = logging.getLogger('model training')
    logger.setLevel(logging.INFO)
    file_handler = logging.FileHandler(log_file)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    return logger

class EvalCallback(object):
    def __init__(self, logger):
        self.logger = logger

    def __call__(self, env):
        results = {}
        for data_name, metric_name, score, _ in env.evaluation_result_list:
            if data_name not in results:
                results[data_name] = {}
            results[data_name][metric_name] = score

        self.logger.info(f"Iteration {env.iteration + 1}\n")
        for data_name in results:
            auc_score = results[data_name].get('auc')
            if auc_score is not None:
                 self.logger.info(f"{data_name} AUC: {auc_score}\n")
        
        


            
def semi_learning(train, test, feat_ver='999', model_ver='999', cst_id='cst_keywrd', target='target', category='auto', is_predict=False):
    
    model_path='/home/<USER>/work/userdata/'
    
    log_file = model_path + '%s_%s_lgb.log' % (feat_ver, model_ver)
    logger = setup_logger(log_file)

    callback = EvalCallback(logger)
    
    X_test = test.copy()
    X_test.drop([cst_id, target], axis=1, inplace=True)
    
    if is_predict:     
        with open(model_path + '%s_%s.json.txt', 'r') as f:
            loaded_params = json.load(f)

        loaded_booster = lgb.Booster(model_file=model_path + '%s_%s.lgb.txt' % (feat_ver, model_ver)) 

        loaded_model = lgb.LGBMClassifier(**loaded_params)
        loaded_model._Booster = loaded_booster

        y_test_pred_proba = loaded_model.predict_proba(X_test)[:, 1]     
        
        return y_test_pred_proba
        
                          
    X = train.copy()
    y = X[target]
    X.drop([cst_id, target], axis=1, inplace=True)
    

    
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, stratify=y, random_state=42)
    
    train_data = lgb.Dataset(X_train, label=y_train)
    val_data = lgb.Dataset(X_val, label=y_val)
    
    eval_set = [(X_val, y_val)]

    model_params = {
        'objective': 'binary',
        'boosting_type': 'gbdt',
        'min_split_gain' :0.0,
        'eval_metric': 'auc',
        'early_stopping_rounds': 200,
        'min_child_weight': 1
    }

    # 创建LightGBM模型
    model = lgb.LGBMClassifier(**model_params)


    param_grid = {
        'num_leaves': [254],
        'learning_rate': [0.005],
        'n_estimators': [5000],
        'scale_pos_weight': [1],
        'feature_fraction' : [0.75],
        'bagging_fraction' : [0.75],
        'max_bin' : [128]
    }
    

    grid = GridSearchCV(
        model,
        param_grid,
        scoring='roc_auc',
        cv=5,
        n_jobs=-1,
        refit=False
    )
    


    grid.fit(X_train, y_train, eval_set=eval_set)


    best_params = grid.best_params_
    
    model_params.update(best_params)

    best_model = lgb.LGBMClassifier(**model_params)
    
    best_model.fit(
        X_train, y_train,
        eval_set=eval_set,
        eval_metric='auc',
        early_stopping_rounds=200,
        callbacks=[callback]
    )    


    best_model.booster_.save_model(model_path + '%s_%s.lgb.txt' % (feat_ver, model_ver))    
    
    with open(model_path + '%s_%s.lgb.json' % (feat_ver, model_ver), 'w') as f:
        json.dump(model_params, f)    
        
    y_test_pred_proba = best_model.predict_proba(X_test)[:, 1]
  


    feature_importances = best_model.booster_.feature_importance(importance_type='gain')    
    feature_names = X.columns.tolist()


    feature_importances_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importances
    }).sort_values(by='importance', ascending=False)


    feature_importances_df.fillna(value=0, inplace=True)    
    feature_importances_df.to_csv(model_path + 'import%s_%s_lgb.csv' % (feat_ver, model_ver), index=False)
    

    
    return y_test_pred_proba


def run_model(base_date):

    # df_train = pd.read_csv('/home/<USER>/work/fqt/csv/train202503f_20240228.csv', dtype={'cst_keywrd' : 'object'}, nrows=1000)
    # df_test = pd.read_csv('/home/<USER>/work/fqt/csv/test202503f_20240331.csv', dtype={'cst_keywrd' : 'object'}, nrows=1000)
    
    train_date = base_date
    test_date = get_date(train_date, 1)    

    df_train = pd.read_csv(f'/home/<USER>/work/fqt/csv/train202503f_{train_date}.csv', dtype={'cst_keywrd' : 'object'})
    df_test = pd.read_csv(f'/home/<USER>/work/fqt/csv/test202503f_{test_date}.csv', dtype={'cst_keywrd' : 'object'})

    df_train = drop_cols(df_train)
    df_test = drop_cols(df_test)

    df = df_test.copy()


    ############设置项################
    # 4.5_20240201，基于20240201数据进行训练
    # is_train：只预测设置为False
    ##################################
    y_test_pred_proba = semi_learning(df_train, df_test, feat_ver='202503', model_ver=f'4.8_{train_date}')

    pred_df = pd.DataFrame(y_test_pred_proba, columns=['y_lgb2'])
    sub = pd.concat([df['cst_keywrd'], pred_df], axis=1)

    ###########设置项##########
    # 测试数据：subf6.3m4.3_2207t2304_0731.csv
    # 验证数据：subf6.3m4.3_2207t2304_val0630.csv（对应train数据、验证数据）
    ###########################
    sub.to_csv(f'/home/<USER>/work/fqt/csv/subf202503m4.8_{train_date}_{test_date}.csv', index=False)


if __name__ == "__main__":
    if is_jupyter():
#         base_date = input('输入日期：')
        base_date = '20250228'
    else:
        if len(sys.argv) != 2:
            print(f"Usage: {sys.argv[0]} YYYYMMDD")
            sys.exit(1)
        base_date = sys.argv[1]
        
    run_model(base_date)

