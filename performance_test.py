#!/usr/bin/env python
# coding: utf-8

"""
性能对比测试脚本
用于对比优化前后的性能差异
"""

import time
import psutil
import pandas as pd
import numpy as np
from typing import Dict, Any
import logging
from datetime import datetime
import tracemalloc

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, test_name: str):
        self.test_name = test_name
        self.start_time = None
        self.end_time = None
        self.start_memory = None
        self.peak_memory = None
        self.process = psutil.Process()
        
    def __enter__(self):
        """开始监控"""
        logger.info(f"开始性能测试: {self.test_name}")
        
        # 记录开始时间
        self.start_time = time.time()
        
        # 记录开始内存
        self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        # 开始内存跟踪
        tracemalloc.start()
        
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """结束监控"""
        # 记录结束时间
        self.end_time = time.time()
        
        # 记录峰值内存
        current, peak = tracemalloc.get_traced_memory()
        self.peak_memory = peak / 1024 / 1024  # MB
        tracemalloc.stop()
        
        # 计算结果
        execution_time = self.end_time - self.start_time
        end_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = end_memory - self.start_memory
        
        logger.info(f"性能测试完成: {self.test_name}")
        logger.info(f"执行时间: {execution_time:.2f} 秒")
        logger.info(f"内存使用: 开始 {self.start_memory:.2f}MB, "
                   f"结束 {end_memory:.2f}MB, "
                   f"增加 {memory_increase:.2f}MB")
        logger.info(f"峰值内存: {self.peak_memory:.2f}MB")
        
        return {
            'test_name': self.test_name,
            'execution_time': execution_time,
            'start_memory': self.start_memory,
            'end_memory': end_memory,
            'memory_increase': memory_increase,
            'peak_memory': self.peak_memory
        }


def create_test_data(n_rows: int = 10000) -> pd.DataFrame:
    """
    创建测试数据
    
    Args:
        n_rows: 行数
        
    Returns:
        测试数据框
    """
    logger.info(f"创建测试数据: {n_rows} 行")
    
    np.random.seed(42)
    
    data = {
        'cst_keywrd': [f'CUST_{i:06d}' for i in range(n_rows)],
        'target': np.random.binomial(1, 0.1, n_rows),
        'call_dt': (pd.date_range('2022-01-01', periods=min(n_rows, 1000), freq='D').tolist() * (n_rows // 1000 + 1))[:n_rows],
        'age': np.random.randint(18, 80, n_rows),
        'gnd_cd': np.random.choice(['M', 'F'], n_rows),
        'mar_sttn_cd': np.random.choice([1, 2, 3, 4], n_rows),
        'mo_incmam': np.random.normal(5000, 2000, n_rows),
        'cst_star_cd': np.random.choice([1, 2, 3, 4, 5], n_rows),
    }
    
    # 添加更多分类特征
    for i in range(10):
        data[f'feature_{i}_ind'] = np.random.choice([0, 1], n_rows)
        data[f'feature_{i}_cd'] = np.random.choice(range(1, 11), n_rows)
    
    # 添加更多数值特征
    for i in range(20):
        data[f'numeric_{i}'] = np.random.normal(0, 1, n_rows)
    
    df = pd.DataFrame(data)
    logger.info(f"测试数据创建完成: {df.shape}")
    return df


def test_beta_encoder_performance():
    """测试BetaEncoder性能"""
    from fv202503_optimized import OptimizedBetaEncoder
    
    # 创建测试数据
    test_data = create_test_data(50000)
    
    # 测试优化后的编码器
    with PerformanceMonitor("OptimizedBetaEncoder") as monitor:
        encoder = OptimizedBetaEncoder('gnd_cd')
        encoder.fit(test_data, 'target')
        encoded_values = encoder.transform(test_data, 'mean')
    
    optimized_result = monitor.__exit__(None, None, None)
    
    logger.info("BetaEncoder性能测试完成")
    return optimized_result


def test_data_processing_performance():
    """测试数据处理性能"""
    from fv202503_optimized import optimize_data_types, clean_and_convert_data, Config
    
    # 创建测试数据
    test_data = create_test_data(100000)
    config = Config()
    
    # 测试数据类型优化
    with PerformanceMonitor("数据类型优化") as monitor:
        optimized_data = optimize_data_types(test_data.copy())
    
    optimization_result = monitor.__exit__(None, None, None)
    
    # 测试数据清洗
    with PerformanceMonitor("数据清洗") as monitor:
        cleaned_data = clean_and_convert_data(test_data.copy(), config)
    
    cleaning_result = monitor.__exit__(None, None, None)
    
    logger.info("数据处理性能测试完成")
    return optimization_result, cleaning_result


def test_memory_efficiency():
    """测试内存效率"""
    from fv202503_optimized import optimize_data_types
    
    # 创建大型测试数据
    test_data = create_test_data(200000)
    
    logger.info("测试内存效率...")
    
    # 原始数据内存使用
    original_memory = test_data.memory_usage(deep=True).sum() / 1024 / 1024
    logger.info(f"原始数据内存使用: {original_memory:.2f}MB")
    
    # 优化后数据内存使用
    optimized_data = optimize_data_types(test_data.copy())
    optimized_memory = optimized_data.memory_usage(deep=True).sum() / 1024 / 1024
    logger.info(f"优化后数据内存使用: {optimized_memory:.2f}MB")
    
    memory_reduction = (original_memory - optimized_memory) / original_memory * 100
    logger.info(f"内存减少: {memory_reduction:.1f}%")
    
    return {
        'original_memory': original_memory,
        'optimized_memory': optimized_memory,
        'memory_reduction': memory_reduction
    }


def run_performance_tests():
    """运行所有性能测试"""
    logger.info("开始运行性能测试套件...")
    
    results = {}
    
    try:
        # 测试BetaEncoder性能
        logger.info("=" * 50)
        logger.info("测试BetaEncoder性能")
        results['beta_encoder'] = test_beta_encoder_performance()
        
        # 测试数据处理性能
        logger.info("=" * 50)
        logger.info("测试数据处理性能")
        opt_result, clean_result = test_data_processing_performance()
        results['data_optimization'] = opt_result
        results['data_cleaning'] = clean_result
        
        # 测试内存效率
        logger.info("=" * 50)
        logger.info("测试内存效率")
        results['memory_efficiency'] = test_memory_efficiency()
        
        # 输出总结
        logger.info("=" * 50)
        logger.info("性能测试总结")
        for test_name, result in results.items():
            if isinstance(result, dict) and 'execution_time' in result:
                logger.info(f"{test_name}: {result['execution_time']:.2f}秒, "
                           f"内存增加: {result['memory_increase']:.2f}MB")
        
        return results
        
    except Exception as e:
        logger.error(f"性能测试失败: {e}")
        raise


if __name__ == "__main__":
    run_performance_tests()
