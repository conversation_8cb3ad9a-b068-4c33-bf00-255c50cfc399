import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def generate_example_data(output_file="data.csv", num_records=1000, num_ids=50):
    """
    生成用于测试特征工程的示例数据
    
    Parameters:
    output_file (str): 输出CSV文件的路径
    num_records (int): 要生成的记录数
    num_ids (int): 不同ID的数量
    """
    # 设置随机种子以确保可重复性
    np.random.seed(42)
    random.seed(42)
    
    # 生成基准日期（当前日期）
    current_date = datetime.now()
    
    # 创建空的DataFrame
    data = []
    
    # 生成ID列表
    ids = [f"ID_{i:03d}" for i in range(1, num_ids + 1)]
    
    # 生成数据
    for _ in range(num_records):
        # 随机选择一个ID
        id_value = random.choice(ids)
        
        # 随机生成日期（过去一年内）
        days_ago = random.randint(0, 365)
        date = current_date - timedelta(days=days_ago)
        date_str = date.strftime("%Y-%m-%d")
        
        # 随机生成类型（1-5）
        lx_value = random.randint(1, 5)
        
        # 随机生成持续时间（1-120分钟）
        tm_value = random.randint(1, 120)
        
        # 随机生成次数（1-10）
        cs_value = random.randint(1, 10)
        
        # 添加到数据列表
        data.append({
            "rq": date_str,
            "id": id_value,
            "lx": lx_value,
            "tm": tm_value,
            "cs": cs_value
        })
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 保存到CSV
    df.to_csv(output_file, index=False, encoding="utf-8-sig")
    
    print(f"已生成示例数据，共 {num_records} 条记录，保存至 {output_file}")
    return df

if __name__ == "__main__":
    generate_example_data()
    print("示例数据生成完成！") 