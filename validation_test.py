#!/usr/bin/env python
# coding: utf-8

"""
功能验证测试脚本
验证优化后的程序功能是否正确
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from fv202503_optimized import OptimizedBetaEncoder, Config, optimize_data_types

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_beta_encoder_functionality():
    """测试BetaEncoder功能正确性"""
    logger.info("测试BetaEncoder功能...")
    
    # 创建测试数据
    np.random.seed(42)
    test_data = pd.DataFrame({
        'group': ['A', 'B', 'A', 'B', 'A', 'C', 'C', 'A', 'B', 'C'] * 100,
        'target': np.random.binomial(1, 0.3, 1000)
    })
    
    # 测试编码器
    encoder = OptimizedBetaEncoder('group')
    encoder.fit(test_data, 'target')
    
    # 验证编码结果
    encoded_values = encoder.transform(test_data, 'mean')
    
    # 基本验证
    assert len(encoded_values) == len(test_data), "编码结果长度不匹配"
    assert not np.any(np.isnan(encoded_values)), "编码结果包含NaN"
    assert np.all((encoded_values >= 0) & (encoded_values <= 1)), "编码结果超出[0,1]范围"
    
    logger.info("✅ BetaEncoder功能测试通过")
    return True


def test_data_type_optimization():
    """测试数据类型优化功能"""
    logger.info("测试数据类型优化...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'int_small': np.random.randint(0, 100, 1000),
        'int_large': np.random.randint(0, 100000, 1000),
        'float_col': np.random.normal(0, 1, 1000),
        'cat_col_ind': np.random.choice(['A', 'B', 'C'], 1000),
        'cat_col_cd': np.random.choice([1, 2, 3, 4], 1000),
    })
    
    # 记录原始内存使用
    original_memory = test_data.memory_usage(deep=True).sum()
    
    # 优化数据类型
    optimized_data = optimize_data_types(test_data.copy())
    optimized_memory = optimized_data.memory_usage(deep=True).sum()
    
    # 验证优化效果
    assert optimized_memory < original_memory, "内存使用没有减少"
    assert len(optimized_data) == len(test_data), "数据行数发生变化"
    assert list(optimized_data.columns) == list(test_data.columns), "列名发生变化"
    
    # 验证分类列被正确转换
    assert optimized_data['cat_col_ind'].dtype.name == 'category', "分类列未被转换为category类型"
    assert optimized_data['cat_col_cd'].dtype.name == 'category', "分类列未被转换为category类型"
    
    memory_reduction = (original_memory - optimized_memory) / original_memory * 100
    logger.info(f"✅ 数据类型优化测试通过，内存减少: {memory_reduction:.1f}%")
    return True


def test_config_functionality():
    """测试配置类功能"""
    logger.info("测试配置类功能...")
    
    # 创建配置对象
    config = Config()
    
    # 验证默认配置
    assert hasattr(config, 'base_path'), "配置缺少base_path"
    assert hasattr(config, 'csv_path'), "配置缺少csv_path"
    assert hasattr(config, 'cache_path'), "配置缺少cache_path"
    assert hasattr(config, 'rolling_window_days'), "配置缺少rolling_window_days"
    
    # 验证配置值类型
    assert isinstance(config.rolling_window_days, int), "rolling_window_days应为整数"
    assert isinstance(config.sample_size, int), "sample_size应为整数"
    assert isinstance(config.beta_n_min, int), "beta_n_min应为整数"
    
    logger.info("✅ 配置类功能测试通过")
    return True


def test_vectorized_operations():
    """测试向量化操作"""
    logger.info("测试向量化操作...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'call_conn_num': [10, 0, 5, 8],
        'call_num': [20, 0, 10, 10],
        'instm_cat_num': [2, 0, 1, 3],
        'instm_num': [5, 0, 2, 4],
        'instm_03_num': [1, 0, 1, 2]
    })
    
    # 测试向量化计算
    test_data['call_conn_rate'] = np.where(
        test_data['call_num'] != 0,
        test_data['call_conn_num'] / test_data['call_num'] * 100,
        0
    )
    
    test_data['instm_cat_rate'] = np.where(
        test_data['instm_num'] != 0,
        test_data['instm_cat_num'] / test_data['instm_num'] * 100,
        0
    )
    
    test_data['instm_03_rate'] = np.where(
        test_data['instm_num'] != 0,
        test_data['instm_03_num'] / test_data['instm_num'] * 100,
        0
    )
    
    # 验证计算结果
    expected_call_conn_rate = [50.0, 0.0, 50.0, 80.0]
    expected_instm_cat_rate = [40.0, 0.0, 50.0, 75.0]
    expected_instm_03_rate = [20.0, 0.0, 50.0, 50.0]
    
    np.testing.assert_array_almost_equal(test_data['call_conn_rate'], expected_call_conn_rate)
    np.testing.assert_array_almost_equal(test_data['instm_cat_rate'], expected_instm_cat_rate)
    np.testing.assert_array_almost_equal(test_data['instm_03_rate'], expected_instm_03_rate)
    
    logger.info("✅ 向量化操作测试通过")
    return True


def run_validation_tests():
    """运行所有验证测试"""
    logger.info("开始运行功能验证测试...")
    
    tests = [
        ("BetaEncoder功能", test_beta_encoder_functionality),
        ("数据类型优化", test_data_type_optimization),
        ("配置类功能", test_config_functionality),
        ("向量化操作", test_vectorized_operations),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            logger.info(f"运行测试: {test_name}")
            result = test_func()
            if result:
                passed_tests += 1
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试出错: {e}")
    
    logger.info("=" * 50)
    logger.info(f"验证测试完成: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有功能验证测试通过！")
        return True
    else:
        logger.warning("⚠️ 部分测试未通过，请检查代码")
        return False


if __name__ == "__main__":
    success = run_validation_tests()
    exit(0 if success else 1)
