# 特征工程

这个项目实现了一个特征工程工具，用于从明细数据中计算各种统计指标。

## 数据格式

输入数据应包含以下列：
- `rq` - 日期
- `id` - ID标识
- `lx` - 类型
- `tm` - 持续时间
- `cs` - 次数

## 计算的特征

按ID分组计算以下特征：
1. 上个月累计次数
2. 前半年累计次数
3. 前半年累计时间
4. 类型=3的上个月累计次数
5. 类型=3的前半年累计次数
6. 类型=3的上个月累计时间
7. 类型=3的前半年累计时间

## 使用方法

### 安装依赖

```bash
pip install pandas numpy
```

### 运行演示

```bash
python demo.py
```

这将生成示例数据并运行特征工程，显示结果。

### 使用自己的数据

将您的数据保存为CSV格式，然后修改`feature_engineering.py`中的路径：

```python
input_path = "你的数据.csv"
output_path = "结果.csv"
result_df = feature_engineering(input_path, output_path)
```

## 项目文件

- `feature_engineering.py` - 核心特征工程功能
- `example_data.py` - 生成示例数据的工具
- `demo.py` - 演示完整工作流的脚本
- `README.md` - 项目说明文档

## 注意事项

- 日期列(`rq`)应为标准日期格式，如"YYYY-MM-DD"
- 特征计算基于数据中的最大日期作为"当前日期"
- 所有缺失值会被自动填充为0 