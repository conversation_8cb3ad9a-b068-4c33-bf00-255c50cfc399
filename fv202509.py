#!/usr/bin/env python
# coding: utf-8


import pandas as pd
import numpy as np
import sys
import logging
from typing import Dict, List, Tuple, Optional, Union
from pathlib import Path
from dataclasses import dataclass
from sklearn.model_selection import train_test_split
import lightgbm as lgb
from sklearn.ensemble import RandomForestClassifier
from sklearn import metrics
from sklearn.ensemble import StackingClassifier
from sklearn.model_selection import StratifiedShuffleSplit
from sklearn.model_selection import KFold, StratifiedKFold, GroupKFold
from sklearn.metrics import f1_score
from sklearn import preprocessing
import joblib
import calendar
from tqdm import tqdm
from datetime import datetime
from dateutil.relativedelta import relativedelta
from pandas.tseries.offsets import Day, MonthEnd
from get_date import get_date
from is_jupyter import is_jupyter
from intelligent_feature_selector import apply_intelligent_feature_selection

# 配置pandas显示选项
pd.set_option("display.max_columns", 1500)
pd.set_option("display.max_rows", 1500)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class Config:
    """配置类，管理所有硬编码参数"""
    
    # 文件路径配置
    base_path: str = '/home/<USER>/work/fqt'
    csv_path: str = '/home/<USER>/work/fqt/csv'
    cache_path: str = '/home/<USER>/work/fqt/cache'
    
    # 时间窗口配置
    rolling_window_days: int = 540
    
    # 数据处理配置
    missing_fill_value: Union[str, int] = '1415926'
    numeric_fill_value: int = 9999
    beta_n_min: int = 1
    
    # 采样配置
    sample_size: int = 2000
    
    def __post_init__(self):
        """确保路径存在"""
        Path(self.csv_path).mkdir(parents=True, exist_ok=True)
        Path(self.cache_path).mkdir(parents=True, exist_ok=True)


class OptimizedBetaEncoder:
    """
    优化后的贝叶斯编码器
    
    主要优化：
    1. 添加输入验证
    2. 优化内存使用
    3. 向量化计算
    4. 异常处理
    """
    
    def __init__(self, group_col: str):
        self.group_col = group_col
        self.stats = None
        self.prior_mean = None
        self._fitted = False
        
    def fit(self, df: pd.DataFrame, target_col: str) -> 'OptimizedBetaEncoder':
        """
        拟合编码器
        
        Args:
            df: 训练数据
            target_col: 目标列名
            
        Returns:
            self: 返回自身以支持链式调用
        """
        if df.empty:
            raise ValueError("输入数据不能为空")
        
        if target_col not in df.columns:
            raise ValueError(f"目标列 '{target_col}' 不存在")
            
        if self.group_col not in df.columns:
            raise ValueError(f"分组列 '{self.group_col}' 不存在")
        
        try:
            # 计算先验均值
            self.prior_mean = df[target_col].mean()
            
            # 使用向量化操作计算统计量
            stats = (df.groupby(self.group_col)[target_col]
                    .agg(['sum', 'count'])
                    .rename(columns={'sum': 'n', 'count': 'N'})
                    .reset_index())
            
            self.stats = stats
            self._fitted = True
            
            logger.info(f"BetaEncoder fitted for column '{self.group_col}', "
                       f"prior_mean: {self.prior_mean:.4f}")
            
        except Exception as e:
            logger.error(f"Error fitting BetaEncoder: {e}")
            raise
            
        return self
        
    def transform(self, df: pd.DataFrame, stat_type: str = 'mean', 
                 N_min: int = 1) -> np.ndarray:
        """
        转换数据
        
        Args:
            df: 要转换的数据
            stat_type: 统计类型
            N_min: 最小样本数
            
        Returns:
            转换后的特征值
        """
        if not self._fitted:
            raise ValueError("编码器尚未拟合，请先调用 fit 方法")
            
        if df.empty:
            return np.array([])
            
        try:
            # 合并统计信息
            df_stats = df[[self.group_col]].merge(
                self.stats, on=self.group_col, how='left'
            )
            
            # 处理缺失值
            n = df_stats['n'].fillna(self.prior_mean).values
            N = df_stats['N'].fillna(1.0).values
            
            # 向量化计算贝叶斯统计量
            N_prior = np.maximum(N_min - N, 0)
            alpha_prior = self.prior_mean * N_prior
            beta_prior = (1 - self.prior_mean) * N_prior
            
            alpha = alpha_prior + n
            beta = beta_prior + N - n
            
            # 根据统计类型计算结果
            if stat_type == 'mean':
                result = alpha / (alpha + beta)
            elif stat_type == 'mode':
                result = (alpha - 1) / (alpha + beta - 2)
            elif stat_type == 'median':
                result = (alpha - 1/3) / (alpha + beta - 2/3)
            elif stat_type == 'var':
                result = (alpha * beta) / ((alpha + beta)**2 * (alpha + beta + 1))
            elif stat_type == 'skewness':
                result = (2 * (beta - alpha) * np.sqrt(alpha + beta + 1) / 
                         ((alpha + beta + 2) * np.sqrt(alpha * beta)))
            elif stat_type == 'kurtosis':
                result = (6 * (alpha - beta)**2 * (alpha + beta + 1) - 
                         alpha * beta * (alpha + beta + 2)) / (
                         alpha * beta * (alpha + beta + 2) * (alpha + beta + 3))
            else:
                raise ValueError(f"不支持的统计类型: {stat_type}")
            
            # 处理无效值
            result = np.where(np.isnan(result), np.nanmedian(result), result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error transforming data: {e}")
            raise


def get_column_names() -> Dict[str, List[str]]:
    """
    获取列名配置
    
    Returns:
        包含各种列名列表的字典
    """
    cols_name = [
        'cst_keywrd', 'target', 'call_dt', 'date2', 'current_time', 'crdt_no_ind',
        'age', 'gnd_cd', 'mar_sttn_cd', 'chl_sttn_cd', 'lcs_cd', 'blng_insid_ind',
        'pln_fnc_efct_ind', 'impt_psng_ind', 'ptnl_vip_ind', 'spclvip_ind',
        'stm_evl_cst_grd_cd', 'mo_incmam', 'cstmgr_id_ind', 'best_ctc_tel_ind',
        'best_ctc_tel7_ind', 'pref_msnd_mtdcd_ind', 'rcv_mail_adr_tpcd_ind',
        'entp_adv_mgtppl_ind', 'entp_act_ctrl_psn_ind', 'enlgps_ind',
        'cst_chnl_bsop_ind', 'empchnl_bsop_ind', 'fam_ppn_num_ind', 'rsdnc_sttn_cd',
        'eddgr_cd', 'dgr_cd', 'ocp_cd', 'post_tpcd', 'ttl_tpcd', 'idy_tpcd',
        'create_cust_first_year', 'crt_insid_ind', 'wrk_unit_char_cd', 'wrk_unit_nm_ind',
        'tyni_ccb_cst_ind', 'cust_level_ind', 'cst_star_cd', 'pyrl_cst_ind',
        'crcrd_crline', 'ebnkg_sign_ind', 'mpb_sign_ind', 'tbnk_sign_ind',
        'sms_bnk_sign_ind', 'wechat_bnk_sign_ind', 'mpb_sign_not_actvt_ind',
        'long_py_sign_ind', 'crcrd_sign_auto_repy_ind', 'ms_dep_sign_ind',
        'idv_hsln_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'idv_othr_lncst_ind', 'qckln_cst_ind', 'psnloan_cst_ind', 'carln_cst_ind',
        'brkevn_chrtc_cst_ind', 'non_brkevn_chrtc_cst_ind', 'opn_chmtpd_cst_ind',
        'yuelf_cst_ind', 'acc_cmdty_cst_ind', 'natdbt_cst_ind', 'suying_cst_ind',
        'accgld_atim_cst_ind', 'idvexstl_cst_ind', 'idv_ovsea_rmt_cst_ind',
        'pos_mrch_sign_ind', 'bigamt_ctfofdep_cst_ind', 'stdnt_cst_ind',
        'glblpy_crd_cst_ind', 'idv_frncy_cst_ind', 'agnc_gldexg_cst_ind',
        'crnmo_smsbnk_fee_ind', 'sign_qikpay_ind', 'Cust_Org_Num_ind',
        'CCrdCstEtHPrblScorVal', 'CCrdCstEnmtHPrblSclCd', 'CCrdCstLRHPrblScorVal',
        'CCrdCstLwtRHPrblSclCd', 'CCCHlCcCvPlSc', 'CCCHlCcCvPlScCd'
    ]
    
    # 添加更多列名...（由于长度限制，这里只显示部分）
    
    return {
        'all_columns': cols_name,
        'drop_columns': ['date2', 'current_time'],
        'categorical_suffixes': ['_ind', '_cd', '_tpcd'],
    }


def optimize_data_types(df: pd.DataFrame) -> pd.DataFrame:
    """
    优化数据类型以减少内存使用
    
    Args:
        df: 输入数据框
        
    Returns:
        优化后的数据框
    """
    logger.info("开始优化数据类型...")
    
    original_memory = df.memory_usage(deep=True).sum() / 1024**2
    
    # 优化分类变量
    categorical_cols = [col for col in df.columns 
                       if any(suffix in col for suffix in ['_ind', '_cd', '_tpcd'])]
    
    for col in categorical_cols:
        if col in df.columns:
            df[col] = df[col].astype('category')
    
    # 优化数值变量
    for col in df.select_dtypes(include=['int64']).columns:
        if df[col].min() >= 0:
            if df[col].max() < 255:
                df[col] = df[col].astype('uint8')
            elif df[col].max() < 65535:
                df[col] = df[col].astype('uint16')
            elif df[col].max() < 4294967295:
                df[col] = df[col].astype('uint32')
        else:
            if df[col].min() > -128 and df[col].max() < 127:
                df[col] = df[col].astype('int8')
            elif df[col].min() > -32768 and df[col].max() < 32767:
                df[col] = df[col].astype('int16')
            elif df[col].min() > -2147483648 and df[col].max() < 2147483647:
                df[col] = df[col].astype('int32')
    
    # 优化浮点数
    for col in df.select_dtypes(include=['float64']).columns:
        df[col] = pd.to_numeric(df[col], downcast='float')
    
    optimized_memory = df.memory_usage(deep=True).sum() / 1024**2
    memory_reduction = (original_memory - optimized_memory) / original_memory * 100
    
    logger.info(f"内存使用优化完成: {original_memory:.2f}MB -> {optimized_memory:.2f}MB "
               f"(减少 {memory_reduction:.1f}%)")
    
    return df


def load_data_efficiently(file_path: str, columns: List[str],
                         dtypes: Optional[Dict] = None) -> pd.DataFrame:
    """
    高效加载数据

    Args:
        file_path: 文件路径
        columns: 列名列表
        dtypes: 数据类型字典

    Returns:
        加载的数据框
    """
    try:
        logger.info(f"开始加载数据: {file_path}")

        # 默认数据类型
        if dtypes is None:
            dtypes = {'cst_keywrd': 'object'}

        # 使用更高效的读取参数
        df = pd.read_csv(
            file_path,
            header=None,
            sep=',',
            usecols=range(len(columns)),
            names=columns,
            dtype=dtypes,
            parse_dates=['call_dt'] if 'call_dt' in columns else None,
            low_memory=False,
            engine='c'  # 使用C引擎提高性能
        )

        logger.info(f"数据加载完成: {df.shape}")
        return df

    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        raise


def create_calling_instm_optimized(calling_instm_file: str, config: Config) -> Tuple[pd.DataFrame, ...]:
    """
    优化后的外呼进件数据处理函数

    Args:
        calling_instm_file: 外呼进件文件名
        config: 配置对象

    Returns:
        处理后的数据框元组
    """
    logger.info("开始处理外呼进件数据...")

    cols_name = ['cst_keywrd', 'category', 'apply_dt', 'date2', 'time1', 'instm_pd_tpcd', 'apply_num']

    try:
        # 高效加载数据
        file_path = f"{config.csv_path}/{calling_instm_file}"
        df = pd.read_csv(
            file_path,
            header=None,
            sep=',',
            usecols=[0, 1, 2, 3, 4, 5, 6],
            names=cols_name,
            dtype={'cst_keywrd': 'object'},
            parse_dates=['apply_dt'],
            low_memory=False
        )

        # 过滤数据
        df_call = df[(df.category == 3) & (df.cst_keywrd.notnull())].copy()
        df_instm = df[(df.category == 4) & (df.cst_keywrd.notnull())].copy()
        df_instm_03 = df_instm[df_instm.instm_pd_tpcd == 3].copy()

        # 优化滚动窗口计算
        rolling_window = f"{config.rolling_window_days}D"

        # 外呼数据聚合
        df_call_sorted = df_call.set_index('apply_dt').sort_index()
        df_call_g = (df_call_sorted.groupby('cst_keywrd')[['instm_pd_tpcd', 'apply_num']]
                    .rolling(rolling_window)
                    .sum()
                    .reset_index())
        df_call_g.columns = ['cst_keywrd', 'call_dt', 'call_conn_num', 'call_num']

        # 进件03数据聚合
        df_instm_03_sorted = df_instm_03.set_index('apply_dt').sort_index()
        df_instm_03_g = (df_instm_03_sorted.groupby('cst_keywrd')['apply_num']
                        .rolling(rolling_window)
                        .sum()
                        .reset_index())
        df_instm_03_g.columns = ['cst_keywrd', 'call_dt', 'instm_03_num']

        # 进件数据聚合
        df_instm_sorted = df_instm.set_index('apply_dt').sort_index()
        df_instm_g = (df_instm_sorted.groupby('cst_keywrd')['apply_num']
                     .rolling(rolling_window)
                     .sum()
                     .reset_index())
        df_instm_g = (df_instm_g.groupby(['cst_keywrd', 'apply_dt'])
                     .agg({'apply_num': 'max'})
                     .reset_index())
        df_instm_g.columns = ['cst_keywrd', 'call_dt', 'instm_num']

        # 进件类别数据聚合
        df_instm_cat_g = (df_instm_sorted.groupby('cst_keywrd')['instm_pd_tpcd']
                         .rolling(rolling_window)
                         .apply(pd.Series.nunique, raw=False)
                         .reset_index())
        df_instm_cat_g = (df_instm_cat_g.groupby(['cst_keywrd', 'apply_dt'])
                         .agg({'instm_pd_tpcd': 'max'})
                         .reset_index())
        df_instm_cat_g.columns = ['cst_keywrd', 'call_dt', 'instm_cat_num']

        logger.info("外呼进件数据处理完成")
        return df_call_g, df_instm_03_g, df_instm_g, df_instm_cat_g

    except Exception as e:
        logger.error(f"处理外呼进件数据失败: {e}")
        raise


def get_drop_columns() -> List[str]:
    """
    获取需要删除的列名列表

    Returns:
        需要删除的列名列表
    """
    return [
        'rt12mo_srbsn_ind', 'rt12mo_agncgdepm_txcst_ind', 'Cash_BACK_Cnt_L3',
        'eysv_gld_sign_ind', 'ruralres_ind', 'soldier_ind', 'vchr_natdbt_cst_ind',
        'wlth_mgt_crd_cst_ind', 'prvt_bnk_crd_cst_ind', 'Cash_BACK_Amt_L2',
        'fushun_lcrd_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'natdbt_cst_ind', 'nine_elmt_compl_cst_ind', 'glb_py_crd_cst_ind',
        'hiqlty_idy_cst_ind', 'non_rsdnt_cst_ind', 'Cash_BACK_Amt_L8',
        'gnstg_mopcml_cst_ind', 'othr_fnd_cst_ind', 'ovsea_cst_ind',
        'Cash_BACK_Amt_L11', 'Sum_Draw_Cnt_L5', 'STMT_CASH_INTR_L5',
        'Instm_Grd_Amt_L6', 'Instm_Grd_Amt_L5', 'Instm_Grd_Amt_L3',
        'Rt6_Prd_CsmDnum', 'Instm_Grd_Amt_L2', 'Instm_Grd_Amt_L11',
        'Instm_Fst_Dnum_L9', 'STMT_CASH_INTR_L10', 'STMT_CASH_INTR_L3',
        'STMT_CASH_INTR_L7', 'Sum_Consume_Amt_L6', 'Instm_Fst_Dnum_L3',
        'STMT_RTL_INTR_L3', 'Instm_Fst_Dnum_L11', 'Instm_Fst_Amt_L7',
        'Sum_Consume_Amt', 'Sum_Consume_Amt_L10', 'Instm_Fst_Amt_L5',
        'Sum_Consume_Amt_L2', 'Sum_Consume_Amt_L3', 'Instm_Grd_Amt_L8',
        'Rt12_Prd_Encshmt_Int_MoNum', 'Instm_Grd_Dnum_L3', 'Repy_Dnum_L8',
        'RT3Mo_Cnsmp_Instm_Dnum', 'RT3Mo_InstmT_Amt', 'RT3Mo_InstmT_Dnum',
        'Repy_Amt_L11', 'Repy_Amt_L3', 'Instm_Grd_Dnum_L8', 'Repy_Amt_L7',
        'Repy_Amt_L9', 'Instm_Grd_Dnum_L6', 'Repy_Dnum_L11', 'Repy_Dnum_L3',
        'Repy_Dnum_L5', 'Repy_Dnum_L6', 'Repy_Dnum_L7', 'Sum_Consume_Amt_L4',
        'Sum_Consume_Amt_L8', 'Cash_Tfrout_Cnt_L2', 'CrCrd_HCpln_Trnd_Cst_Ind',
        'bigamt_realest_own_cst_ind', 'bond_cst_ind', 'brkevn_chrtc_cst_ind',
        'Crcrd_Cst_Rt3Mo_ActDeal_Cnt', 'Crcrd_Cst_Rt2Mo_ActDeal_Cnt',
        'ccb_fam_mbr_ind', 'Crcrd_Cst_Rt1Yr_ActDeal_Cnt', 'Crcrd_Cst_Rt1Mo_ActDeal_Cnt',
        'CrCrd_HCpln_Trnd_Dgr_Cd', 'clsd_fnd_cst_ind', 'Sum_Consume_Amt_L9',
        'Cnsmp_Instm_Bill_Bal', 'Cash_Tfrout_Amt_L7', 'Cash_Tfrout_Amt_L3',
        'Cash_Tfrout_Amt_L2', 'dapeng_iccrd_cst_ind', 'dep_cst_ind',
        'Cash_Tfrout_Cnt_L9', 'Cash_Tfrout_Cnt_L3', 'CrnMo_Cnsmp_Instm_Amt',
        'CrnMo_Com_Instm_Dnum', 'CrnMo_InstmT_Amt', 'CrnMo_InstmT_Dnum',
        'Sum_Consume_Cnt_L1', 'Sum_Consume_Cnt_L10', 'Sum_Consume_Cnt_L3',
        'Sum_Consume_Cnt_L5', 'Instm_Fst_Amt_L3', 'Instm_Fst_Amt_L10',
        'Sum_Draw_Amt_L2', 'Sum_Draw_Amt_L3', 'Sum_Draw_Amt_L8', 'Sum_Draw_Amt_L9',
        'Sum_Draw_Cnt_L10', 'Sum_Draw_Cnt_L11', 'Sum_Draw_Cnt_L3', 'CrnPrd_Cnsmp_Amt',
        'Sum_Draw_Cnt_L6', 'Sum_Draw_Cnt_L7', 'acc_fx_sign_ind', 'CrnMo_SpInstm_Amt',
        'antmylndg_clbr_cst_ind', 'xbrdrfnc_cst_ind', 'Cash_BACK_Amt_L9',
        'Sum_Draw_Amt_L10', 'Instm_Grd_Dnum_L7', 'Sum_Consume_Cnt_L8',
        'SpInstm_Cur_Bal', 'STMT_RTL_INTR_L1', 'Rt6_Prd_Encshmt_Amt',
        'RT3Mo_SpInstm_Amt', 'RT3Mo_Cnsmp_Instm_Amt', 'RT12Mo_InstmT_Dnum',
        'Sum_Draw_Amt_L1', 'Instm_Grd_Amt_L1', 'Instm_Fst_Amt_L4', 'Instm_Cur_Bal',
        'CrnMo_SpInstm_Dnum', 'CrnMo_Cnsmp_Instm_Dnum', 'Com_Instm_Cur_Bal',
        'Cash_Tfrout_Amt_L4', 'Instm_Grd_Dnum_L5', 'ruralres_ind',
        'rt12mo_srbsn_ind', 'Instm_Fst_Dnum_L6', 'rt12mo_mopcml_txcst_ind',
        'soldier_ind', 'Cash_BACK_Cnt_L9', 'Cash_BACK_Amt_L3', 'vchr_natdbt_cst_ind',
        'wlth_mgt_crd_cst_ind', 'rt12mo_agncgdepm_txcst_ind', 'hiqlty_idy_cst_ind',
        'qdii_fnd_cst_ind', 'antmylndg_clbr_cst_ind', 'glb_py_crd_cst_ind',
        'eysv_gld_sign_ind', 'dapeng_iccrd_cst_ind', 'clsd_fnd_cst_ind',
        'ccb_fam_mbr_ind', 'brkevn_chrtc_cst_ind', 'bond_cst_ind',
        'bigamt_realest_own_cst_ind', 'best_pn_ind', 'acc_fx_sign_ind',
        'prvt_bnk_crd_cst_ind', 'Sum_Consume_Amt_L5', 'natdbt_cst_ind',
        'nine_elmt_compl_cst_ind', 'non_rsdnt_cst_ind', 'Rt3_Prd_Cnsmp_Amt',
        'othr_fnd_cst_ind', 'Repy_Dnum_L10', 'gnstg_mopcml_cst_ind', 'Repy_Amt',
        'xbrdrfnc_cst_ind', 'stmt_adr_ziped', 'cny_dep_ind', 'Sum_Draw_Cnt_L2',
        'CrnMo_LgTp_Entp_Pch_Cgy_Cnsmp_Amt', 'CrnMo_Tvl_Cgy_CsmDnum',
        'fushun_lcrd_cst_ind', 'RT6Mo_Cnsmp_Instm_Amt', 'Cash_Tfrout_Cnt_L5',
        'Instm_Fst_Dnum', 'Rt3_Prd_DealW_Bill_Instm_Dnum', 'Cash_Tfrout_Cnt',
        'CrnMo_Bill_Instm_Dnum', 'RT3Mo_Com_Instm_Dnum', 'CrnMo_Instm_Dnum',
        'CrnMo_PbHosp_Cgy_CsmDnum', 'Cash_Tfrout_Cnt_L4', 'Cash_BACK_Cnt_L6',
        'RT3Mo_Bill_Instm_Dnum', 'CrnMo_Car_SCgy_CsmDnum',
        'RT3Mo_LgTp_Entp_Pch_Cgy_Cnsmp_Amt', 'RT6Mo_InstmT_Amt',
        'CrnMo_Cash_Instm_Amt', 'RT6Mo_Cnsmp_Instm_Dnum', 'idv_ovsea_rmt_cst_ind',
        'RT6Mo_InstmT_Dnum', 'crcrd_crline', 'PrvtCstCT910DPALDDnum',
        'tyni_ccb_cst_ind', 'tmdep_cst_ind', 'dmddep_cst_ind', 'acc_cmdty_cst_ind',
        'carln_cst_ind', 'RT3Mo_RlEst_Cgy_Cnsmp_Amt', 'CrnMo_LgTp_Entp_Pch_Cgy_CsmDnum',
        'lcs_cd', 'instm_count_ind', 'CrnMo_RlEst_Cgy_Cnsmp_Amt',
        'CrnMo_Cash_Instm_Dnum', 'CrnMo_RlEst_Cgy_CsmDnum', 'ms_dep_sign_ind',
        'non_brkevn_chrtc_cst_ind', 'idvfamcshmgtsvcsgnind_ind', 'agnc_gldexg_cst_ind',
        'opn_chmtpd_cst_ind', 'idv_oprt_lncst_ind', 'idv_cnsmp_lncst_ind',
        'RT3Mo_RlEst_Cgy_CsmDnum', 'bigamt_ctfofdep_cst_ind', 'qckln_cst_ind',
        'accgld_atim_cst_ind', 'Cash_BACK_Cnt_L4', 'Cash_BACK_Cnt',
        'STMT_RTL_INTR', 'STMT_RTL_INTR_L4', 'Rt3_Prd_Encshmt_Dnum',
        'RT3Mo_SpInstm_Dnum', 'RT6Mo_Cash_Instm_Dnum', 'PrvtCstCT910DZCFDDnum',
        'Cash_BACK_Amt', 'CrnMo_TaxPymt_Cgy_CsmDnum', 'estb_1_cgy_acc_cst_ind',
        'Rt6_Prd_DealW_Bill_Instm_Dnum', 'Cash_BACK_Amt_L4', 'CrnMo_HcBty_Cgy_CsmDnum',
        'RT3Mo_LgTp_Entp_Pch_Cgy_CsmDnum', 'Instm_Fst_Dnum_L2', 'Sum_Draw_Cnt_L4',
        'Cash_Tfrout_Amt', 'RT3Mo_Car_SCgy_CsmDnum', 'RT3Mo_Cash_Instm_Dnum',
        'CrnPrd_Encshmt_Dnum', 'apply_num_ind', 'Cash_Tfrout_Cnt_L6_ind',
        'Cash_BACK_Cnt_L5', 'idv_frncy_cst_ind', 'Cash_BACK_Amt_L5',
        'Sum_Draw_Cnt', 'Sum_Draw_Amt', 'mpb_sign_not_actvt_ind', 'instm_count_ind',
        'RT3Mo_TaxPymt_Cgy_CsmDnum', 'Sum_Draw_Amt_L4', 'Cash_BACK_Amt_L1',
        'pcrd_cst_ind', 'stk_fnd_cst_ind', 'ccrd_vld_cst_ind', 'ccrdcst_ind',
        'RT3Mo_PbHosp_Cgy_Cnsmp_Amt', 'epos_sign_ind', 'Cash_Tfrout_Amt_L5',
        'CrnMo_Com_Instm_Amt', 'Instm_Fst_Amt_L2', 'holdhhedmblph_cst_ind',
        'CrnMo_Tvl_Cgy_Cnsmp_Amt', 'CrnMo_Car_SCgy_Cnsmp_Amt', 'CrnMo_Bill_Instm_Amt',
        'Sum_Draw_Amt_L6', 'idvexstl_cst_ind', 'gld_ostck_sign_ind',
        'RT3Mo_Tvl_Cgy_CsmDnum', 'fstfl_cst_ind', 'Instm_Fst_Dnum_L5',
        'Instm_Fst_Dnum_L4', 'C_Cash_Use_Rate', 'rt12mo_acpsmtl_txcst_ind',
        'Sum_Draw_Cnt_L1', 'RT6Mo_SpInstm_Dnum', 'holdnwtpmblph_cst_ind',
        'stdntprft_svc_sgn_ind', 'impt_psng_ind', 'RT3Mo_Cash_Instm_Amt',
        'RT6Mo_Com_Instm_Dnum', 'CrnPrd_Encshmt_Amt', 'RT3Mo_PbHosp_Cgy_CsmDnum',
        'RT6Mo_Bill_Instm_Dnum', 'STMT_CASH_INTR', 'RT3Mo_Instm_Dnum',
        'wechat_bnk_sign_ind', 'CrnMo_PbHosp_Cgy_Cnsmp_Amt',
        'CrnMo_LgHmAppls_Cgy_CsmDnum', 'instm_nunique_ind', 'Cash_Tfrout_Amt_L6',
        'Instm_Fst_Dnum_L1', 'CrnMo_Whlsl_Cgy_CsmDnum', 'instm_count_ind',
        'apply_num_ind'
    ]


def create_features_optimized(in_file: str, config: Config) -> pd.DataFrame:
    """
    优化后的特征工程主函数

    Args:
        in_file: 输入文件名
        config: 配置对象

    Returns:
        处理后的数据框
    """
    logger.info(f"开始处理特征工程: {in_file}")

    # 获取列名配置
    column_config = get_column_names()
    cols_name = column_config['all_columns']

    try:
        # 高效加载数据
        file_path = f"{config.csv_path}/{in_file}"
        df_cust = load_data_efficiently(
            file_path,
            cols_name,
            {'cst_keywrd': 'object'}
        )

        # 删除不需要的列
        col_drops = column_config['drop_columns']
        df_cust.drop(col_drops, inplace=True, axis=1, errors='ignore')

        # 数据清洗和类型转换
        df_cust = clean_and_convert_data(df_cust, config)

        # 优化数据类型
        df_cust = optimize_data_types(df_cust)

        # 加载缓存的外呼进件数据
        cache_files = {
            'df_call_g': f'{config.cache_path}/df_call_g.pkl',
            'df_instm_g': f'{config.cache_path}/df_instm_g.pkl',
            'df_instm_03_g': f'{config.cache_path}/df_instm_03_g.pkl',
            'df_instm_cat_g': f'{config.cache_path}/df_instm_cat_g.pkl'
        }

        cached_data = {}
        for name, path in cache_files.items():
            try:
                cached_data[name] = joblib.load(path)
                logger.info(f"成功加载缓存: {name}")
            except FileNotFoundError:
                logger.warning(f"缓存文件不存在: {path}")
                raise

        # 合并外呼进件特征
        df_cust = merge_calling_features(df_cust, cached_data, config)

        # 计算衍生特征
        df_cust = calculate_derived_features(df_cust)

        # 保存处理后的数据
        cache_file = f'{config.cache_path}/df_cust.pkl'
        joblib.dump(df_cust, cache_file)
        logger.info(f"特征工程完成，数据已保存: {cache_file}")

        return df_cust

    except Exception as e:
        logger.error(f"特征工程处理失败: {e}")
        raise


def clean_and_convert_data(df: pd.DataFrame, config: Config) -> pd.DataFrame:
    """
    数据清洗和类型转换

    Args:
        df: 输入数据框
        config: 配置对象

    Returns:
        清洗后的数据框
    """
    logger.info("开始数据清洗和类型转换...")

    # 处理特殊列的缺失值和类型转换
    special_columns = {
        'mpbcstroumpmodllstdyr': config.numeric_fill_value,
        'ocp_cd': config.numeric_fill_value,
        'crt_insid_ind': None,
        'stmt_adr_zipecd_ind': None
    }

    for col, fill_value in special_columns.items():
        if col in df.columns:
            if fill_value is not None:
                df[col].fillna(fill_value, inplace=True)
            df[col] = pd.to_numeric(df[col], errors='coerce')

    logger.info("数据清洗和类型转换完成")
    return df


def merge_calling_features(df_cust: pd.DataFrame, cached_data: Dict,
                          config: Config) -> pd.DataFrame:
    """
    合并外呼进件特征

    Args:
        df_cust: 客户数据
        cached_data: 缓存的外呼进件数据
        config: 配置对象

    Returns:
        合并后的数据框
    """
    logger.info("开始合并外呼进件特征...")

    # 获取唯一的客户外呼记录
    df_call = df_cust.drop_duplicates(subset=['cst_keywrd', 'call_dt'])[['cst_keywrd', 'call_dt']]

    # 定义时间窗口
    time_window = pd.Timedelta(config.rolling_window_days, 'D')

    # 合并各类外呼进件数据
    merge_configs = [
        ('df_call_g', ['call_conn_num', 'call_num']),
        ('df_instm_g', ['instm_num']),
        ('df_instm_03_g', ['instm_03_num']),
        ('df_instm_cat_g', ['instm_cat_num'])
    ]

    for data_name, feature_cols in merge_configs:
        df_cust = merge_time_window_features(
            df_cust, df_call, cached_data[data_name],
            time_window, feature_cols
        )

    logger.info("外呼进件特征合并完成")
    return df_cust


def merge_time_window_features(df_cust: pd.DataFrame, df_call: pd.DataFrame,
                              feature_data: pd.DataFrame, time_window: pd.Timedelta,
                              feature_cols: List[str]) -> pd.DataFrame:
    """
    合并时间窗口特征

    Args:
        df_cust: 客户数据
        df_call: 外呼数据
        feature_data: 特征数据
        time_window: 时间窗口
        feature_cols: 特征列名

    Returns:
        合并后的数据框
    """
    # 合并数据
    df_merged = df_call.merge(feature_data, on=['cst_keywrd'], how='left')

    # 筛选时间窗口内的数据
    time_condition = (
        (df_merged.call_dt_x - df_merged.call_dt_y >= pd.Timedelta(0, 'D')) &
        (df_merged.call_dt_x - df_merged.call_dt_y < time_window)
    )
    df_filtered = df_merged[time_condition]

    # 获取最新的特征值
    df_latest = (df_filtered.groupby(['cst_keywrd', 'call_dt_x'])
                .agg({col: 'max' for col in feature_cols + ['call_dt_y']})
                .reset_index())

    df_latest.rename(columns={'call_dt_x': 'call_dt'}, inplace=True)

    # 合并到主数据框
    df_cust = df_cust.merge(df_latest, on=['cst_keywrd', 'call_dt'], how='left')

    return df_cust


def calculate_derived_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算衍生特征

    Args:
        df: 输入数据框

    Returns:
        添加衍生特征后的数据框
    """
    logger.info("开始计算衍生特征...")

    # 向量化计算衍生特征
    df['call_conn_rate'] = np.where(
        df['call_num'] != 0,
        df['call_conn_num'] / df['call_num'] * 100,
        0
    )

    df['instm_cat_rate'] = np.where(
        df['instm_num'] != 0,
        df['instm_cat_num'] / df['instm_num'] * 100,
        0
    )

    df['instm_03_rate'] = np.where(
        df['instm_num'] != 0,
        df['instm_03_num'] / df['instm_num'] * 100,
        0
    )

    logger.info("衍生特征计算完成")
    return df


def create_csv_output(df: pd.DataFrame, out_file: str, config: Config) -> None:
    """
    创建CSV输出文件

    Args:
        df: 数据框
        out_file: 输出文件名
        config: 配置对象
    """
    try:
        # 保存完整数据
        full_path = f"{config.csv_path}/{out_file}"
        df.to_csv(full_path, index=False)
        logger.info(f"完整数据已保存: {full_path}")

        # 保存采样数据
        if len(df) > config.sample_size:
            df_sample = df.sample(config.sample_size, random_state=42)
            sample_file = out_file.replace('.csv', '_s.csv')
            sample_path = f"{config.csv_path}/{sample_file}"
            df_sample.to_csv(sample_path, index=False)
            logger.info(f"采样数据已保存: {sample_path}")

    except Exception as e:
        logger.error(f"保存CSV文件失败: {e}")
        raise


def generate_data_optimized(in_train: str, in_test: str, calling_instm_file: str,
                           out_train: str, out_test: str, out_val: str,
                           feat_ver: str, train_date: str, config: Config,
                           cache_level: int = 0, is_train: bool = True) -> None:
    """
    优化后的数据生成主函数

    Args:
        in_train: 训练数据文件名
        in_test: 测试数据文件名
        calling_instm_file: 外呼进件文件名
        out_train: 输出训练文件名
        out_test: 输出测试文件名
        out_val: 输出验证文件名
        feat_ver: 特征版本
        train_date: 训练日期
        config: 配置对象
        cache_level: 缓存级别
        is_train: 是否仅训练模式
    """
    logger.info("开始数据生成流程...")
    start_time = datetime.now()

    try:
        # 步骤1: 处理外呼进件数据
        if cache_level <= 0:
            logger.info("开始处理外呼进件数据...")
            df_call_g, df_instm_03_g, df_instm_g, df_instm_cat_g = create_calling_instm_optimized(
                calling_instm_file, config
            )

            # 保存缓存
            cache_files = {
                'df_call_g.pkl': df_call_g,
                'df_instm_g.pkl': df_instm_g,
                'df_instm_03_g.pkl': df_instm_03_g,
                'df_instm_cat_g.pkl': df_instm_cat_g
            }

            for filename, data in cache_files.items():
                cache_path = f"{config.cache_path}/{filename}"
                joblib.dump(data, cache_path)
                logger.info(f"缓存已保存: {cache_path}")

        # 步骤2: 特征工程
        if cache_level <= 1:
            logger.info("开始训练数据特征工程...")
            df_train = create_features_optimized(in_train, config)
            joblib.dump(df_train, f'{config.cache_path}/df_train{feat_ver}.pkl')

            if not is_train:
                logger.info("开始测试数据特征工程...")
                df_test = create_features_optimized(in_test, config)
                joblib.dump(df_test, f'{config.cache_path}/df_test{feat_ver}.pkl')

        # 步骤3: 加载缓存数据（如果使用缓存）
        if cache_level == 2:
            logger.info("从缓存加载数据...")
            df_train = joblib.load(f'{config.cache_path}/df_train{feat_ver}.pkl')
            if not is_train:
                df_test = joblib.load(f'{config.cache_path}/df_test{feat_ver}.pkl')

        # 步骤4: 目标编码
        logger.info("开始目标编码...")
        df_train, df_test_encoded = apply_target_encoding(
            df_train,
            df_test if not is_train else None,
            train_date,
            config
        )

        # 步骤5: 数据分割和输出
        logger.info("开始数据分割和输出...")
        create_train_val_test_splits(
            df_train, df_test_encoded, train_date,
            out_train, out_test, out_val,
            config, is_train
        )

        end_time = datetime.now()
        runtime = end_time - start_time
        logger.info(f"数据生成完成，总耗时: {runtime}")

    except Exception as e:
        logger.error(f"数据生成失败: {e}")
        raise


def apply_target_encoding(df_train: pd.DataFrame, df_test: Optional[pd.DataFrame],
                         train_date: str, config: Config) -> Tuple[pd.DataFrame, Optional[pd.DataFrame]]:
    """
    应用目标编码

    Args:
        df_train: 训练数据
        df_test: 测试数据
        train_date: 训练日期
        config: 配置对象

    Returns:
        编码后的训练和测试数据
    """
    logger.info("开始目标编码...")

    # 确保日期列为datetime类型
    df_train['call_dt'] = pd.to_datetime(df_train['call_dt'])
    if df_test is not None:
        df_test['call_dt'] = pd.to_datetime(df_test['call_dt'])

    # 识别分类列
    cat_cols = [col for col in df_train.columns
               if any(suffix in col for suffix in ['_ind', '_cd', '_tpcd'])]
    num_cols = [col for col in df_train.columns
               if not any(suffix in col for suffix in ['_ind', '_cd', '_tpcd'])]

    logger.info(f"识别到 {len(cat_cols)} 个分类列，{len(num_cols)} 个数值列")

    # 设置编码训练数据范围
    train_start_date = pd.to_datetime('2022-1-31')
    train_end_date = pd.to_datetime(train_date)
    enc_df_train = df_train[
        (df_train.call_dt >= train_start_date) &
        (df_train.call_dt <= train_end_date)
    ].copy()

    # 应用目标编码
    feature_cols = []

    with tqdm(total=len(cat_cols), desc="目标编码进度") as pbar:
        for col in cat_cols:
            try:
                # 创建并拟合编码器
                encoder = OptimizedBetaEncoder(col)
                encoder.fit(enc_df_train, 'target')

                # 编码特征名
                feature_name = f'{col}_mean'

                # 处理缺失值
                df_train[col] = df_train[col].fillna(config.missing_fill_value)
                df_train[feature_name] = encoder.transform(df_train, 'mean', config.beta_n_min)

                if df_test is not None:
                    df_test[col] = df_test[col].fillna(config.missing_fill_value)
                    df_test[feature_name] = encoder.transform(df_test, 'mean', config.beta_n_min)

                feature_cols.append(feature_name)
                pbar.set_description(f'处理列: {col}')
                pbar.update(1)

            except Exception as e:
                logger.warning(f"编码列 {col} 时出错: {e}")
                continue

    # 选择最终特征
    final_cols = num_cols + feature_cols
    df_train_encoded = df_train[final_cols].copy()
    df_test_encoded = df_test[final_cols].copy() if df_test is not None else None

    logger.info(f"目标编码完成，编码前特征数: {len(final_cols)}")

    # 应用智能特征选择
    logger.info("开始智能特征选择...")

    # 准备特征选择的数据（排除非特征列）
    feature_cols_for_selection = [col for col in final_cols
                                 if col not in ['cst_keywrd', 'target', 'call_dt']]

    if len(feature_cols_for_selection) > 50:  # 只有特征数量足够多时才进行选择
        X_for_selection = df_train_encoded[feature_cols_for_selection]
        y_for_selection = df_train_encoded['target']

        # 应用智能特征选择
        X_selected, selected_features = apply_intelligent_feature_selection(
            X_for_selection, y_for_selection, target_features=None
        )

        # 更新最终列列表
        final_selected_cols = ['cst_keywrd', 'target', 'call_dt'] + selected_features
        df_train_encoded = df_train_encoded[final_selected_cols].copy()

        if df_test_encoded is not None:
            test_selected_cols = ['cst_keywrd', 'call_dt'] + selected_features
            df_test_encoded = df_test_encoded[test_selected_cols].copy()

        logger.info(f"智能特征选择完成，最终特征数: {len(selected_features)}")
        logger.info(f"特征选择比例: {len(selected_features)/len(feature_cols_for_selection):.2%}")
    else:
        logger.info("特征数量较少，跳过智能特征选择")

    return df_train_encoded, df_test_encoded


def create_train_val_test_splits(df_train: pd.DataFrame, df_test: Optional[pd.DataFrame],
                                train_date: str, out_train: str, out_test: str,
                                out_val: str, config: Config, is_train: bool) -> None:
    """
    创建训练、验证、测试数据分割

    Args:
        df_train: 训练数据
        df_test: 测试数据
        train_date: 训练日期
        out_train: 输出训练文件名
        out_test: 输出测试文件名
        out_val: 输出验证文件名
        config: 配置对象
        is_train: 是否仅训练模式
    """
    logger.info("开始数据分割...")

    # 设置日期范围
    train_start_date = pd.to_datetime('2022-2-28')
    train_end_date = pd.to_datetime(train_date)
    val_date = pd.to_datetime(train_date)

    # 分割训练和验证数据
    train_data = df_train[
        (df_train.call_dt >= train_start_date) &
        (df_train.call_dt <= train_end_date)
    ].copy()

    val_data = df_train[df_train.call_dt == val_date].copy()

    # 删除日期列
    train_data.drop(['call_dt'], axis=1, inplace=True)
    val_data.drop(['call_dt'], axis=1, inplace=True)

    if df_test is not None:
        test_data = df_test.copy()
        test_data.drop(['call_dt'], axis=1, inplace=True)

    logger.info(f"训练数据形状: {train_data.shape}")
    logger.info(f"验证数据形状: {val_data.shape}")
    if df_test is not None:
        logger.info(f"测试数据形状: {test_data.shape}")

    # 保存数据
    logger.info("保存训练数据...")
    create_csv_output(train_data, out_train, config)

    logger.info("保存验证数据...")
    create_csv_output(val_data, out_val, config)

    if not is_train and df_test is not None:
        logger.info("保存测试数据...")
        create_csv_output(test_data, out_test, config)

    logger.info("数据分割和保存完成")


def run_feature_engineering_optimized(base_date: str) -> None:
    """
    运行优化后的特征工程流程

    Args:
        base_date: 基准日期
    """
    start_time = datetime.now()
    logger.info(f"程序开始时间: {start_time}")

    try:
        # 初始化配置
        config = Config()

        # 设置日期
        train_date = base_date
        test_date = get_date(train_date, 1)

        # 生成数据
        generate_data_optimized(
            in_train=f'train8_{train_date}.csv',
            in_test=f'test8_{test_date}.csv',
            calling_instm_file=f'call_instm_{train_date}.csv',
            out_train=f'train202503f_{train_date}.csv',
            out_test=f'test202503f_{test_date}.csv',
            out_val=f'val202503f_{train_date}.csv',
            feat_ver=f'test202503_{train_date}',
            train_date=train_date,
            config=config,
            cache_level=0,
            is_train=False
        )

        end_time = datetime.now()
        runtime = end_time - start_time
        logger.info(f"程序结束时间: {end_time}")
        logger.info(f"程序总运行时间: {runtime}")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise


if __name__ == "__main__":
    if is_jupyter():
        base_date = input('输入日期：')
    else:
        if len(sys.argv) != 2:
            print(f"Usage: {sys.argv[0]} YYYYMMDD")
            sys.exit(1)
        base_date = sys.argv[1]

    run_feature_engineering_optimized(base_date)
