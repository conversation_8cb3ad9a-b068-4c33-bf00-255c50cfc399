import sys
import time
from pyhive import hive
from thrift.transport import TTransport
from sqlalchemy import create_engine,text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, String, Integer
from con_getcsv import con_getcsv

def connect_to_hive():
    try:
        engine = create_engine('hive://n110nai01hd0001.ksc.com:10000/db_1119_bd_platform', connect_args={'auth':'KERBEROS', 'kerberos_service_name' : 'hive'})
        return engine
    except Exception as e:
        print(f"Error connecting to Hive: {e}")
        return None


def get_table_count(engine, table_name):
    try:
        with engine.connect() as connection:
            result = connection.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            count = result.scalar()
        return count
    except SQLAlchemyError as e:
        print(f"Error querying table {table_name}: {e}")
        return None
    

def check_table_counts(engine, tables_to_check):
    remaining_tables = []
    for table, expected_count in tables_to_check:
        actual_count = get_table_count(engine, table)
        if actual_count is None:
            remaining_tables.append((table, expected_count))
        elif actual_count == expected_count:
            print(f"Table {table} count matches. Expected: {expected_count}, Actual: {actual_count}")
        else:
            print(f"Table {table} count mismatch. Expected: {expected_count}, Actual: {actual_count}")
            remaining_tables.append((table, expected_count))
    return remaining_tables


def invoke_con_getcsv(args):
    if len(args) != 5:
        print("Usage: python script.py <output_value> <count1> <count2> <count3>")
        return
    


    train_dt = args[1]
    expected_counts = [int(arg) for arg in args[2:]]

    if all(count == 0 for count in expected_counts):
        con_getcsv(train_dt)
        return


    tables = ['dim_jx_trans', 'dim_jx_trans_test', 'dim_jx_trans_var']

    engine = connect_to_hive()
    if not engine:
        return

    tables_to_check = list(zip(tables, expected_counts))


    while tables_to_check:
        tables_to_check = check_table_counts(engine, tables_to_check)
        if not tables_to_check:
            con_getcsv(train_dt)
            break

        print(f"等待表：{tables_to_check}，数据接入！")
        time.sleep(60) 

    engine.dispose()

if __name__ == "__main__":
    invoke_con_getcsv(sys.argv)