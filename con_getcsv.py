import concurrent.futures
import subprocess
import os
import sys
import re
from datetime import datetime, timedelta
from tqdm import tqdm
import calendar


def get_date(base_date, months_to_add):
    base_date = datetime.strptime(base_date, "%Y%m%d")
    year = base_date.year
    month = base_date.month + months_to_add

    year += (month - 1) // 12
    month = (month - 1) % 12 + 1

    last_day = calendar.monthrange(year, month)[1]

    return datetime(year, month, last_day).strftime("%Y%m%d")

def execute_beeline(table, output_file, pbar, expected_rows):

    command = f"beeline --verbose=true --outputformat=csv2 -e \"select * from {table}\""

    try:
        process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1, universal_newlines=True)

        processed_rows = 0
        pbar.total = expected_rows
        pbar.refresh()


        with open(output_file, 'w') as out:
            # 跳过前4行
            for _ in range(4):
                next(process.stdout)

            for line in process.stdout:
                out.write(line)
                processed_rows += 1
                if processed_rows % 1000 == 0:
                    pbar.update(min(1000, pbar.total - pbar.n))

        pbar.update(pbar.total - pbar.n)

        stderr = process.stderr.read()
        return_code = process.wait()

        if return_code != 0:
            raise subprocess.CalledProcessError(return_code, command, stderr=stderr)

        return True, None

    except subprocess.CalledProcessError as e:
        print(f"Error executing command for table {table}:{str(e)}")
        return False
    except Exception as e:
        print(f"An error occurred while processing table {table}:{str(e)}")
        return False

def process_table(table, date, expected_rows):
    output_dir = "/home/<USER>/work/fqt/csv"
    os.makedirs(output_dir, exist_ok=True)

    if table == "db_1119_bd_platform.dim_jx_trans":
        output_file = os.path.join(output_dir, f"train8_{date}.csv")
    elif table == "db_1119_bd_platform.dim_jx_trans_test":
        next_month_end = get_date(date, 1)
        output_file = os.path.join(output_dir, f"test8_{next_month_end}.csv")
    elif table == "db_1119_bd_platform.dim_jx_trans_var":
        output_file = os.path.join(output_dir, f"call_instm_{date}.csv")

    with tqdm(total=expected_rows, desc=f"Processing {table}", unit=" rows", position=1, leave=False) as pbar:
        success = execute_beeline(table, output_file, pbar, expected_rows)
    return table, success

def con_getcsv(date, *table_sizes):

    table_sizes = [int(size) if int(size) > 0 else 2000000 for size in table_sizes]

    tables = ["db_1119_bd_platform.dim_jx_trans", "db_1119_bd_platform.dim_jx_trans_test", "db_1119_bd_platform.dim_jx_trans_var"]

    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = {executor.submit(process_table, table, date, size): table for table, size in zip(tables, table_sizes)}

        with tqdm(total=len(tables), desc="Overall progress", position=0) as pbar:
            for future in concurrent.futures.as_completed(futures):
                table = futures[future]
                try:
                    _, success = future.result()
                    if success:
                        pbar.update(1)
                        pbar.set_postfix_str(f"Completed: {table}")
                    else:
                        pbar.set_postfix_str(f"Failed: {table}")
                except Exception as exc:
                    print(f"{table} generated an exception: {exc}")
                    pbar.set_postfix_str(f"Error: {table}")

if __name__ == "__main__":
    if len(sys.argv) != 5:
        print("Usage: python3 script.py YYYYMMDD size1 size2 size3")
        print("Use 0 for any size to use the default value of 200")
        sys.exit(1)

    date = sys.argv[1]
    table_sizes = [int(size) if int(size) > 0 else 2000000 for size in sys.argv[2:]]
    con_getcsv(date, *table_sizes)