import subprocess
import csv
import os
from datetime import datetime, timedelta
import calendar

def execute_beeline_command(query):
    command = f'beeline --verbose=true --outputformat=csv2 -e "{query}"'
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    return result.stdout.splitlines()[4:]  

def save_to_csv(data, filename, output_dir):
    os.makedirs(output_dir, exist_ok=True)
    filepath = os.path.join(output_dir, filename)
    with open(filepath, 'w', newline='') as f:
        writer = csv.writer(f)
        for row in data:
            writer.writerow(row.split(','))


def get_date(base_date, months_to_add):
    base_date = datetime.strptime(base_date, "%Y%m%d")
    year = base_date.year
    month = base_date.month + months_to_add
    
    # Adjust year if month overflows
    year += (month - 1) // 12
    month = (month - 1) % 12 + 1
    
    # Get the last day of the calculated month
    last_day = calendar.monthrange(year, month)[1]
    
    return datetime(year, month, last_day).strftime("%Y%m%d")


def getcsv(base_date):
    output_dir = '/home/<USER>/work/fqt/csv'
    
    queries = [
        ("select * from db_1119_bd_platform.dim_jx_trans", "train8", 0),
        ("select * from db_1119_bd_platform.dim_jx_trans_test", "test8", 1),
        ("select * from db_1119_bd_platform.dim_jx_trans_var", "call_instm", 0)
    ]

    for query, prefix, months_to_add in queries:
        data = execute_beeline_command(query)
        filename = f"{prefix}_{get_date(base_date, months_to_add)}.csv"
        save_to_csv(data, filename, output_dir)
        print(f"Saved {filename}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 2:
        print("Usage: python script.py YYYYMMDD")
        sys.exit(1)
    
    base_date = sys.argv[1]
    getcsv(base_date)    

