from example_data import generate_example_data
from feature_engineering import feature_engineering
import pandas as pd

def main():
    """
    完整的工作流演示：生成示例数据并进行特征工程
    """
    print("=== 特征工程演示 ===")
    print("\n1. 生成示例数据")
    
    # 生成示例数据
    data_path = "sample_data.csv"
    generate_example_data(output_file=data_path, num_records=500, num_ids=20)
    
    # 显示原始数据样例
    print("\n原始数据示例:")
    raw_data = pd.read_csv(data_path)
    print(raw_data.head())
    print(f"数据形状: {raw_data.shape}")
    
    # 数据概览
    print("\n数据概览:")
    print(raw_data.describe())
    
    # ID分布
    print("\nID分布:")
    print(raw_data['id'].value_counts().head())
    
    print("\n2. 执行特征工程")
    # 执行特征工程
    output_path = "feature_engineering_result.csv"
    result = feature_engineering(data_path, output_path)
    
    # 显示结果
    print("\n特征工程结果示例:")
    print(result.head())
    print(f"结果数据形状: {result.shape}")
    
    # 结果列
    print("\n结果包含的特征:")
    for col in result.columns:
        print(f"- {col}")
    
    print(f"\n结果已保存到 {output_path}")
    print("\n=== 演示完成 ===")

if __name__ == "__main__":
    main() 