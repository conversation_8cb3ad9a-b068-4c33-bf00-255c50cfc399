#!/usr/bin/env python
# coding: utf-8

"""
增强的模型训练器
支持多算法集成、高级优化策略，预期AUC提升：0.03-0.08
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score
from sklearn.ensemble import VotingClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression
import lightgbm as lgb
import xgboost as xgb
from sklearn.model_selection import GridSearchCV
import joblib
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class EnhancedModelTrainer:
    """
    增强的模型训练器
    
    主要功能：
    1. 多算法集成（LightGBM + XGBoost）
    2. 类别平衡处理
    3. 高级超参数优化
    4. Stacking集成
    5. 交叉验证优化
    """
    
    def __init__(self, config):
        self.config = config
        self.models = {}
        self.ensemble_model = None
        self.feature_names = None
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置专用日志记录器"""
        logger = logging.getLogger(f'EnhancedTrainer_{id(self)}')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def train_ensemble_model(self, df_train: pd.DataFrame, 
                           cst_id: str = 'cst_keywrd', 
                           target: str = 'target') -> Dict[str, Any]:
        """
        训练集成模型
        
        Args:
            df_train: 训练数据
            cst_id: 客户ID列名
            target: 目标列名
            
        Returns:
            训练结果字典
        """
        try:
            self.logger.info("开始增强模型训练...")
            
            # 准备训练数据
            X = df_train.drop([cst_id, target], axis=1)
            y = df_train[target]
            self.feature_names = X.columns.tolist()
            
            # 数据分割
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, 
                test_size=self.config.test_size,
                stratify=y,
                random_state=self.config.random_state
            )
            
            self.logger.info(f"数据分割完成 - 训练: {X_train.shape}, 验证: {X_val.shape}")
            self.logger.info(f"目标变量分布 - 训练集: {y_train.value_counts().to_dict()}")
            
            # 处理类别不平衡
            X_train_balanced, y_train_balanced = self._handle_class_imbalance(X_train, y_train)
            
            # 训练基础模型
            base_models = self._train_base_models(X_train_balanced, y_train_balanced, X_val, y_val)
            
            # 训练集成模型
            ensemble_model = self._train_ensemble_model(
                base_models, X_train_balanced, y_train_balanced, X_val, y_val
            )
            
            # 模型评估
            train_results = self._evaluate_ensemble_model(
                ensemble_model, X_train, y_train, X_val, y_val
            )
            
            self.ensemble_model = ensemble_model
            self.models = base_models
            
            self.logger.info("增强模型训练完成")
            return train_results
            
        except Exception as e:
            self.logger.error(f"增强模型训练失败: {e}")
            raise
    
    def _handle_class_imbalance(self, X_train: pd.DataFrame, y_train: pd.Series) -> Tuple[pd.DataFrame, pd.Series]:
        """处理类别不平衡"""
        try:
            # 检查类别分布
            class_counts = y_train.value_counts()
            imbalance_ratio = class_counts.min() / class_counts.max()
            
            self.logger.info(f"类别不平衡比例: {imbalance_ratio:.3f}")
            
            if imbalance_ratio < 0.3:  # 如果不平衡严重
                self.logger.info("检测到严重类别不平衡，应用SMOTE...")
                
                try:
                    from imblearn.over_sampling import SMOTE
                    smote = SMOTE(random_state=self.config.random_state)
                    X_balanced, y_balanced = smote.fit_resample(X_train, y_train)
                    
                    self.logger.info(f"SMOTE后数据形状: {X_balanced.shape}")
                    self.logger.info(f"SMOTE后类别分布: {pd.Series(y_balanced).value_counts().to_dict()}")
                    
                    return pd.DataFrame(X_balanced, columns=X_train.columns), pd.Series(y_balanced)
                    
                except ImportError:
                    self.logger.warning("SMOTE不可用，使用class_weight处理不平衡")
                    return X_train, y_train
            else:
                self.logger.info("类别分布相对平衡，无需特殊处理")
                return X_train, y_train
                
        except Exception as e:
            self.logger.warning(f"类别平衡处理失败: {e}")
            return X_train, y_train
    
    def _train_base_models(self, X_train: pd.DataFrame, y_train: pd.Series,
                          X_val: pd.DataFrame, y_val: pd.Series) -> Dict[str, Any]:
        """训练基础模型"""
        base_models = {}
        
        # LightGBM模型
        self.logger.info("训练LightGBM模型...")
        lgb_model = self._train_lightgbm(X_train, y_train, X_val, y_val)
        base_models['lightgbm'] = lgb_model
        
        # XGBoost模型
        self.logger.info("训练XGBoost模型...")
        xgb_model = self._train_xgboost(X_train, y_train, X_val, y_val)
        base_models['xgboost'] = xgb_model
        
        # 评估基础模型
        for name, model in base_models.items():
            val_pred = model.predict_proba(X_val)[:, 1]
            val_auc = roc_auc_score(y_val, val_pred)
            self.logger.info(f"{name} 验证集AUC: {val_auc:.4f}")
        
        return base_models
    
    def _train_lightgbm(self, X_train: pd.DataFrame, y_train: pd.Series,
                       X_val: pd.DataFrame, y_val: pd.Series) -> lgb.LGBMClassifier:
        """训练LightGBM模型"""
        # 扩展的超参数搜索空间
        param_grid = {
            'num_leaves': [31, 63, 127, 255],
            'learning_rate': [0.01, 0.05, 0.1],
            'n_estimators': [1000, 2000, 3000],
            'feature_fraction': [0.7, 0.8, 0.9],
            'bagging_fraction': [0.7, 0.8, 0.9],
            'max_depth': [-1, 6, 8, 10],
            'min_child_samples': [20, 50, 100],
            'reg_alpha': [0, 0.1, 0.5],
            'reg_lambda': [0, 0.1, 0.5]
        }
        
        # 基础参数
        base_params = {
            'objective': 'binary',
            'boosting_type': 'gbdt',
            'metric': 'auc',
            'verbose': -1,
            'random_state': self.config.random_state,
            'n_jobs': -1
        }
        
        # 网格搜索
        lgb_model = lgb.LGBMClassifier(**base_params)
        
        grid_search = GridSearchCV(
            estimator=lgb_model,
            param_grid=param_grid,
            scoring='roc_auc',
            cv=3,
            n_jobs=-1,
            verbose=1
        )
        
        grid_search.fit(X_train, y_train)
        
        best_model = grid_search.best_estimator_
        self.logger.info(f"LightGBM最佳参数: {grid_search.best_params_}")
        self.logger.info(f"LightGBM最佳CV AUC: {grid_search.best_score_:.4f}")
        
        return best_model
    
    def _train_xgboost(self, X_train: pd.DataFrame, y_train: pd.Series,
                      X_val: pd.DataFrame, y_val: pd.Series) -> xgb.XGBClassifier:
        """训练XGBoost模型"""
        # XGBoost超参数搜索空间
        param_grid = {
            'max_depth': [3, 6, 9],
            'learning_rate': [0.01, 0.05, 0.1],
            'n_estimators': [1000, 2000],
            'subsample': [0.7, 0.8, 0.9],
            'colsample_bytree': [0.7, 0.8, 0.9],
            'reg_alpha': [0, 0.1, 0.5],
            'reg_lambda': [0, 0.1, 0.5]
        }
        
        # 基础参数
        base_params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'random_state': self.config.random_state,
            'n_jobs': -1,
            'verbosity': 0
        }
        
        # 网格搜索
        xgb_model = xgb.XGBClassifier(**base_params)
        
        grid_search = GridSearchCV(
            estimator=xgb_model,
            param_grid=param_grid,
            scoring='roc_auc',
            cv=3,
            n_jobs=-1,
            verbose=1
        )
        
        grid_search.fit(X_train, y_train)
        
        best_model = grid_search.best_estimator_
        self.logger.info(f"XGBoost最佳参数: {grid_search.best_params_}")
        self.logger.info(f"XGBoost最佳CV AUC: {grid_search.best_score_:.4f}")
        
        return best_model
    
    def _train_ensemble_model(self, base_models: Dict[str, Any], 
                             X_train: pd.DataFrame, y_train: pd.Series,
                             X_val: pd.DataFrame, y_val: pd.Series) -> StackingClassifier:
        """训练Stacking集成模型"""
        self.logger.info("训练Stacking集成模型...")
        
        # 准备基础估计器
        estimators = [(name, model) for name, model in base_models.items()]
        
        # 元学习器
        meta_learner = LogisticRegression(random_state=self.config.random_state, max_iter=1000)
        
        # Stacking分类器
        stacking_model = StackingClassifier(
            estimators=estimators,
            final_estimator=meta_learner,
            cv=3,
            n_jobs=-1,
            verbose=1
        )
        
        # 训练集成模型
        stacking_model.fit(X_train, y_train)
        
        # 评估集成模型
        val_pred = stacking_model.predict_proba(X_val)[:, 1]
        val_auc = roc_auc_score(y_val, val_pred)
        self.logger.info(f"Stacking集成模型验证集AUC: {val_auc:.4f}")
        
        return stacking_model
    
    def _evaluate_ensemble_model(self, ensemble_model: StackingClassifier,
                                X_train: pd.DataFrame, y_train: pd.Series,
                                X_val: pd.DataFrame, y_val: pd.Series) -> Dict[str, Any]:
        """评估集成模型"""
        try:
            # 预测
            y_train_pred = ensemble_model.predict(X_train)
            y_train_proba = ensemble_model.predict_proba(X_train)[:, 1]
            y_val_pred = ensemble_model.predict(X_val)
            y_val_proba = ensemble_model.predict_proba(X_val)[:, 1]
            
            # 计算指标
            results = {
                'train_auc': roc_auc_score(y_train, y_train_proba),
                'val_auc': roc_auc_score(y_val, y_val_proba),
                'train_f1': f1_score(y_train, y_train_pred),
                'val_f1': f1_score(y_val, y_val_pred),
                'train_precision': precision_score(y_train, y_train_pred),
                'val_precision': precision_score(y_val, y_val_pred),
                'train_recall': recall_score(y_train, y_train_pred),
                'val_recall': recall_score(y_val, y_val_pred)
            }
            
            # 记录评估结果
            self.logger.info("集成模型评估结果:")
            for metric, value in results.items():
                self.logger.info(f"  {metric}: {value:.4f}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"集成模型评估失败: {e}")
            raise
    
    def predict(self, df_test: pd.DataFrame, cst_id: str = 'cst_keywrd') -> np.ndarray:
        """使用集成模型进行预测"""
        try:
            if self.ensemble_model is None:
                raise ValueError("集成模型尚未训练")
            
            # 准备测试数据
            X_test = df_test.drop([cst_id], axis=1, errors='ignore')
            if 'target' in X_test.columns:
                X_test = X_test.drop(['target'], axis=1)
            
            # 预测
            y_pred_proba = self.ensemble_model.predict_proba(X_test)[:, 1]
            
            self.logger.info(f"集成模型预测完成，样本数: {len(y_pred_proba)}")
            self.logger.info(f"预测概率范围: [{y_pred_proba.min():.4f}, {y_pred_proba.max():.4f}]")
            
            return y_pred_proba
            
        except Exception as e:
            self.logger.error(f"集成模型预测失败: {e}")
            raise
    
    def save_ensemble_model(self, feat_ver: str, model_ver: str, model_path: str) -> None:
        """保存集成模型"""
        try:
            if self.ensemble_model is None:
                raise ValueError("集成模型尚未训练")
            
            # 保存集成模型
            ensemble_file = f"{model_path}/ensemble_{feat_ver}_{model_ver}.pkl"
            joblib.dump(self.ensemble_model, ensemble_file)
            
            # 保存基础模型
            base_models_file = f"{model_path}/base_models_{feat_ver}_{model_ver}.pkl"
            joblib.dump(self.models, base_models_file)
            
            self.logger.info(f"集成模型已保存:")
            self.logger.info(f"  集成模型: {ensemble_file}")
            self.logger.info(f"  基础模型: {base_models_file}")
            
        except Exception as e:
            self.logger.error(f"保存集成模型失败: {e}")
            raise
    
    def load_ensemble_model(self, feat_ver: str, model_ver: str, model_path: str) -> None:
        """加载集成模型"""
        try:
            # 加载集成模型
            ensemble_file = f"{model_path}/ensemble_{feat_ver}_{model_ver}.pkl"
            self.ensemble_model = joblib.load(ensemble_file)
            
            # 加载基础模型
            base_models_file = f"{model_path}/base_models_{feat_ver}_{model_ver}.pkl"
            self.models = joblib.load(base_models_file)
            
            self.logger.info(f"集成模型加载完成: {ensemble_file}")
            
        except Exception as e:
            self.logger.error(f"加载集成模型失败: {e}")
            raise
